// Code generated by ent, DO NOT EDIT.

package downtimeincident

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the downtimeincident type in the database.
	Label = "downtime_incident"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldServerID holds the string denoting the server_id field in the database.
	FieldServerID = "server_id"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldEndTime holds the string denoting the end_time field in the database.
	FieldEndTime = "end_time"
	// FieldDuration holds the string denoting the duration field in the database.
	FieldDuration = "duration"
	// FieldStatusBefore holds the string denoting the status_before field in the database.
	FieldStatusBefore = "status_before"
	// FieldStatusAfter holds the string denoting the status_after field in the database.
	FieldStatusAfter = "status_after"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// EdgeServer holds the string denoting the server edge name in mutations.
	EdgeServer = "server"
	// Table holds the table name of the downtimeincident in the database.
	Table = "downtime_incidents"
	// ServerTable is the table that holds the server relation/edge.
	ServerTable = "downtime_incidents"
	// ServerInverseTable is the table name for the Server entity.
	// It exists in this package in order to avoid circular dependency with the "server" package.
	ServerInverseTable = "servers"
	// ServerColumn is the table column denoting the server relation/edge.
	ServerColumn = "server_id"
)

// Columns holds all SQL columns for downtimeincident fields.
var Columns = []string{
	FieldID,
	FieldServerID,
	FieldStartTime,
	FieldEndTime,
	FieldDuration,
	FieldStatusBefore,
	FieldStatusAfter,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// ServerIDValidator is a validator for the "server_id" field. It is called by the builders before save.
	ServerIDValidator func(string) error
	// DefaultStartTime holds the default value on creation for the "start_time" field.
	DefaultStartTime func() time.Time
	// StatusBeforeValidator is a validator for the "status_before" field. It is called by the builders before save.
	StatusBeforeValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the DowntimeIncident queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByServerID orders the results by the server_id field.
func ByServerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldServerID, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByEndTime orders the results by the end_time field.
func ByEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTime, opts...).ToFunc()
}

// ByDuration orders the results by the duration field.
func ByDuration(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDuration, opts...).ToFunc()
}

// ByStatusBefore orders the results by the status_before field.
func ByStatusBefore(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatusBefore, opts...).ToFunc()
}

// ByStatusAfter orders the results by the status_after field.
func ByStatusAfter(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatusAfter, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByServerField orders the results by server field.
func ByServerField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newServerStep(), sql.OrderByField(field, opts...))
	}
}
func newServerStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ServerInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ServerTable, ServerColumn),
	)
}
