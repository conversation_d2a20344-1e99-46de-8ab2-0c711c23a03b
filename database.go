package main

import (
	"time"
)

// DatabaseInterface defines the interface for database operations
type DatabaseInterface interface {
	// Server operations
	SaveServer(server *Server) error
	GetServer(id string) (*Server, error)
	GetAllServers() ([]*Server, error)
	DeleteServer(id string) error

	// DowntimeIncident operations
	SaveDowntimeIncident(incident *DowntimeIncident) error
	GetDowntimeIncidents(serverID string, limit int, offset int, startDate *time.Time, endDate *time.Time) ([]*DowntimeIncident, error)
	GetDowntimeStats(serverID string, days int) (*DowntimeStats, error)
	GetOngoingDowntimeIncident(serverID string) (*DowntimeIncident, error)

	// EmailConfig operations
	SaveEmailConfig(config *EmailConfig) error
	GetEmailConfig() (*EmailConfig, error)

	// Connection management
	Close() error
}

// Server represents a server to monitor
type Server struct {
	ID                   string     `json:"id"`
	Name                 string     `json:"name"`
	Host                 string     `json:"host"`
	Status               string     `json:"status"`
	Latency              float64    `json:"latency"`        // Ping latency in milliseconds
	CheckInterval        int        `json:"check_interval"` // Individual check interval in seconds
	LastDown             *time.Time `json:"last_down,omitempty"`
	LastDowntimeDuration int64      `json:"last_downtime_duration"` // Duration of last downtime in seconds
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

// DowntimeIncident represents a downtime incident for a server
type DowntimeIncident struct {
	ID           string     `json:"id"`
	ServerID     string     `json:"server_id"`
	StartTime    time.Time  `json:"start_time"`
	EndTime      *time.Time `json:"end_time,omitempty"`     // Null if incident is ongoing
	Duration     *int64     `json:"duration,omitempty"`     // Duration in seconds, null if ongoing
	StatusBefore string     `json:"status_before"`          // Status before downtime (usually "up")
	StatusAfter  *string    `json:"status_after,omitempty"` // Status after recovery, null if ongoing
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// DowntimeStats represents downtime statistics for a server
type DowntimeStats struct {
	TotalIncidents       int        `json:"total_incidents"`
	TotalDowntimeHours   float64    `json:"total_downtime_hours"`
	AverageIncidentHours float64    `json:"average_incident_hours"`
	UptimePercentage     float64    `json:"uptime_percentage"`
	LastIncidentDate     *time.Time `json:"last_incident_date,omitempty"`
}

// EmailConfig represents email notification settings
type EmailConfig struct {
	ID       string `json:"id"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	To       string `json:"to"`
	Enabled  bool   `json:"enabled"`
}
