package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Server holds the schema definition for the Server entity.
type Server struct {
	ent.Schema
}

// Fields of the Server.
func (Server) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			Unique().
			Immutable(),
		field.String("name").
			NotEmpty(),
		field.String("host").
			NotEmpty(),
		field.String("status").
			Default("unknown"),
		field.Float("latency").
			Default(0),
		field.Int("check_interval").
			Default(5).
			Min(1),
		field.Time("last_down").
			Optional().
			Nillable(),
		field.Int64("last_downtime_duration").
			Default(0),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Server.
func (Server) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("downtime_incidents", DowntimeIncident.Type),
	}
}
