// Code generated by ent, DO NOT EDIT.

package ent

import (
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/schema"
	"server-monitor/ent/server"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	downtimeincidentFields := schema.DowntimeIncident{}.Fields()
	_ = downtimeincidentFields
	// downtimeincidentDescServerID is the schema descriptor for server_id field.
	downtimeincidentDescServerID := downtimeincidentFields[1].Descriptor()
	// downtimeincident.ServerIDValidator is a validator for the "server_id" field. It is called by the builders before save.
	downtimeincident.ServerIDValidator = downtimeincidentDescServerID.Validators[0].(func(string) error)
	// downtimeincidentDescStartTime is the schema descriptor for start_time field.
	downtimeincidentDescStartTime := downtimeincidentFields[2].Descriptor()
	// downtimeincident.DefaultStartTime holds the default value on creation for the start_time field.
	downtimeincident.DefaultStartTime = downtimeincidentDescStartTime.Default.(func() time.Time)
	// downtimeincidentDescStatusBefore is the schema descriptor for status_before field.
	downtimeincidentDescStatusBefore := downtimeincidentFields[5].Descriptor()
	// downtimeincident.StatusBeforeValidator is a validator for the "status_before" field. It is called by the builders before save.
	downtimeincident.StatusBeforeValidator = downtimeincidentDescStatusBefore.Validators[0].(func(string) error)
	// downtimeincidentDescCreatedAt is the schema descriptor for created_at field.
	downtimeincidentDescCreatedAt := downtimeincidentFields[7].Descriptor()
	// downtimeincident.DefaultCreatedAt holds the default value on creation for the created_at field.
	downtimeincident.DefaultCreatedAt = downtimeincidentDescCreatedAt.Default.(func() time.Time)
	// downtimeincidentDescUpdatedAt is the schema descriptor for updated_at field.
	downtimeincidentDescUpdatedAt := downtimeincidentFields[8].Descriptor()
	// downtimeincident.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	downtimeincident.DefaultUpdatedAt = downtimeincidentDescUpdatedAt.Default.(func() time.Time)
	// downtimeincident.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	downtimeincident.UpdateDefaultUpdatedAt = downtimeincidentDescUpdatedAt.UpdateDefault.(func() time.Time)
	emailconfigFields := schema.EmailConfig{}.Fields()
	_ = emailconfigFields
	// emailconfigDescHost is the schema descriptor for host field.
	emailconfigDescHost := emailconfigFields[1].Descriptor()
	// emailconfig.HostValidator is a validator for the "host" field. It is called by the builders before save.
	emailconfig.HostValidator = emailconfigDescHost.Validators[0].(func(string) error)
	// emailconfigDescPort is the schema descriptor for port field.
	emailconfigDescPort := emailconfigFields[2].Descriptor()
	// emailconfig.DefaultPort holds the default value on creation for the port field.
	emailconfig.DefaultPort = emailconfigDescPort.Default.(int)
	// emailconfigDescFrom is the schema descriptor for from field.
	emailconfigDescFrom := emailconfigFields[5].Descriptor()
	// emailconfig.FromValidator is a validator for the "from" field. It is called by the builders before save.
	emailconfig.FromValidator = emailconfigDescFrom.Validators[0].(func(string) error)
	// emailconfigDescTo is the schema descriptor for to field.
	emailconfigDescTo := emailconfigFields[6].Descriptor()
	// emailconfig.ToValidator is a validator for the "to" field. It is called by the builders before save.
	emailconfig.ToValidator = emailconfigDescTo.Validators[0].(func(string) error)
	// emailconfigDescEnabled is the schema descriptor for enabled field.
	emailconfigDescEnabled := emailconfigFields[7].Descriptor()
	// emailconfig.DefaultEnabled holds the default value on creation for the enabled field.
	emailconfig.DefaultEnabled = emailconfigDescEnabled.Default.(bool)
	serverFields := schema.Server{}.Fields()
	_ = serverFields
	// serverDescName is the schema descriptor for name field.
	serverDescName := serverFields[1].Descriptor()
	// server.NameValidator is a validator for the "name" field. It is called by the builders before save.
	server.NameValidator = serverDescName.Validators[0].(func(string) error)
	// serverDescHost is the schema descriptor for host field.
	serverDescHost := serverFields[2].Descriptor()
	// server.HostValidator is a validator for the "host" field. It is called by the builders before save.
	server.HostValidator = serverDescHost.Validators[0].(func(string) error)
	// serverDescStatus is the schema descriptor for status field.
	serverDescStatus := serverFields[3].Descriptor()
	// server.DefaultStatus holds the default value on creation for the status field.
	server.DefaultStatus = serverDescStatus.Default.(string)
	// serverDescLatency is the schema descriptor for latency field.
	serverDescLatency := serverFields[4].Descriptor()
	// server.DefaultLatency holds the default value on creation for the latency field.
	server.DefaultLatency = serverDescLatency.Default.(float64)
	// serverDescCheckInterval is the schema descriptor for check_interval field.
	serverDescCheckInterval := serverFields[5].Descriptor()
	// server.DefaultCheckInterval holds the default value on creation for the check_interval field.
	server.DefaultCheckInterval = serverDescCheckInterval.Default.(int)
	// server.CheckIntervalValidator is a validator for the "check_interval" field. It is called by the builders before save.
	server.CheckIntervalValidator = serverDescCheckInterval.Validators[0].(func(int) error)
	// serverDescLastDowntimeDuration is the schema descriptor for last_downtime_duration field.
	serverDescLastDowntimeDuration := serverFields[7].Descriptor()
	// server.DefaultLastDowntimeDuration holds the default value on creation for the last_downtime_duration field.
	server.DefaultLastDowntimeDuration = serverDescLastDowntimeDuration.Default.(int64)
	// serverDescCreatedAt is the schema descriptor for created_at field.
	serverDescCreatedAt := serverFields[8].Descriptor()
	// server.DefaultCreatedAt holds the default value on creation for the created_at field.
	server.DefaultCreatedAt = serverDescCreatedAt.Default.(func() time.Time)
	// serverDescUpdatedAt is the schema descriptor for updated_at field.
	serverDescUpdatedAt := serverFields[9].Descriptor()
	// server.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	server.DefaultUpdatedAt = serverDescUpdatedAt.Default.(func() time.Time)
	// server.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	server.UpdateDefaultUpdatedAt = serverDescUpdatedAt.UpdateDefault.(func() time.Time)
}
