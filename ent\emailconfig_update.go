// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// EmailConfigUpdate is the builder for updating EmailConfig entities.
type EmailConfigUpdate struct {
	config
	hooks    []Hook
	mutation *EmailConfigMutation
}

// Where appends a list predicates to the EmailConfigUpdate builder.
func (ecu *EmailConfigUpdate) Where(ps ...predicate.EmailConfig) *EmailConfigUpdate {
	ecu.mutation.Where(ps...)
	return ecu
}

// SetHost sets the "host" field.
func (ecu *EmailConfigUpdate) SetHost(s string) *EmailConfigUpdate {
	ecu.mutation.SetHost(s)
	return ecu
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillableHost(s *string) *EmailConfigUpdate {
	if s != nil {
		ecu.SetHost(*s)
	}
	return ecu
}

// SetPort sets the "port" field.
func (ecu *EmailConfigUpdate) SetPort(i int) *EmailConfigUpdate {
	ecu.mutation.ResetPort()
	ecu.mutation.SetPort(i)
	return ecu
}

// SetNillablePort sets the "port" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillablePort(i *int) *EmailConfigUpdate {
	if i != nil {
		ecu.SetPort(*i)
	}
	return ecu
}

// AddPort adds i to the "port" field.
func (ecu *EmailConfigUpdate) AddPort(i int) *EmailConfigUpdate {
	ecu.mutation.AddPort(i)
	return ecu
}

// SetUsername sets the "username" field.
func (ecu *EmailConfigUpdate) SetUsername(s string) *EmailConfigUpdate {
	ecu.mutation.SetUsername(s)
	return ecu
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillableUsername(s *string) *EmailConfigUpdate {
	if s != nil {
		ecu.SetUsername(*s)
	}
	return ecu
}

// ClearUsername clears the value of the "username" field.
func (ecu *EmailConfigUpdate) ClearUsername() *EmailConfigUpdate {
	ecu.mutation.ClearUsername()
	return ecu
}

// SetPassword sets the "password" field.
func (ecu *EmailConfigUpdate) SetPassword(s string) *EmailConfigUpdate {
	ecu.mutation.SetPassword(s)
	return ecu
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillablePassword(s *string) *EmailConfigUpdate {
	if s != nil {
		ecu.SetPassword(*s)
	}
	return ecu
}

// ClearPassword clears the value of the "password" field.
func (ecu *EmailConfigUpdate) ClearPassword() *EmailConfigUpdate {
	ecu.mutation.ClearPassword()
	return ecu
}

// SetFrom sets the "from" field.
func (ecu *EmailConfigUpdate) SetFrom(s string) *EmailConfigUpdate {
	ecu.mutation.SetFrom(s)
	return ecu
}

// SetNillableFrom sets the "from" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillableFrom(s *string) *EmailConfigUpdate {
	if s != nil {
		ecu.SetFrom(*s)
	}
	return ecu
}

// SetTo sets the "to" field.
func (ecu *EmailConfigUpdate) SetTo(s string) *EmailConfigUpdate {
	ecu.mutation.SetTo(s)
	return ecu
}

// SetNillableTo sets the "to" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillableTo(s *string) *EmailConfigUpdate {
	if s != nil {
		ecu.SetTo(*s)
	}
	return ecu
}

// SetEnabled sets the "enabled" field.
func (ecu *EmailConfigUpdate) SetEnabled(b bool) *EmailConfigUpdate {
	ecu.mutation.SetEnabled(b)
	return ecu
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (ecu *EmailConfigUpdate) SetNillableEnabled(b *bool) *EmailConfigUpdate {
	if b != nil {
		ecu.SetEnabled(*b)
	}
	return ecu
}

// Mutation returns the EmailConfigMutation object of the builder.
func (ecu *EmailConfigUpdate) Mutation() *EmailConfigMutation {
	return ecu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ecu *EmailConfigUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, ecu.sqlSave, ecu.mutation, ecu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ecu *EmailConfigUpdate) SaveX(ctx context.Context) int {
	affected, err := ecu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ecu *EmailConfigUpdate) Exec(ctx context.Context) error {
	_, err := ecu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ecu *EmailConfigUpdate) ExecX(ctx context.Context) {
	if err := ecu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ecu *EmailConfigUpdate) check() error {
	if v, ok := ecu.mutation.Host(); ok {
		if err := emailconfig.HostValidator(v); err != nil {
			return &ValidationError{Name: "host", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.host": %w`, err)}
		}
	}
	if v, ok := ecu.mutation.From(); ok {
		if err := emailconfig.FromValidator(v); err != nil {
			return &ValidationError{Name: "from", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.from": %w`, err)}
		}
	}
	if v, ok := ecu.mutation.To(); ok {
		if err := emailconfig.ToValidator(v); err != nil {
			return &ValidationError{Name: "to", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.to": %w`, err)}
		}
	}
	return nil
}

func (ecu *EmailConfigUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ecu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(emailconfig.Table, emailconfig.Columns, sqlgraph.NewFieldSpec(emailconfig.FieldID, field.TypeString))
	if ps := ecu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ecu.mutation.Host(); ok {
		_spec.SetField(emailconfig.FieldHost, field.TypeString, value)
	}
	if value, ok := ecu.mutation.Port(); ok {
		_spec.SetField(emailconfig.FieldPort, field.TypeInt, value)
	}
	if value, ok := ecu.mutation.AddedPort(); ok {
		_spec.AddField(emailconfig.FieldPort, field.TypeInt, value)
	}
	if value, ok := ecu.mutation.Username(); ok {
		_spec.SetField(emailconfig.FieldUsername, field.TypeString, value)
	}
	if ecu.mutation.UsernameCleared() {
		_spec.ClearField(emailconfig.FieldUsername, field.TypeString)
	}
	if value, ok := ecu.mutation.Password(); ok {
		_spec.SetField(emailconfig.FieldPassword, field.TypeString, value)
	}
	if ecu.mutation.PasswordCleared() {
		_spec.ClearField(emailconfig.FieldPassword, field.TypeString)
	}
	if value, ok := ecu.mutation.From(); ok {
		_spec.SetField(emailconfig.FieldFrom, field.TypeString, value)
	}
	if value, ok := ecu.mutation.To(); ok {
		_spec.SetField(emailconfig.FieldTo, field.TypeString, value)
	}
	if value, ok := ecu.mutation.Enabled(); ok {
		_spec.SetField(emailconfig.FieldEnabled, field.TypeBool, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ecu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{emailconfig.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ecu.mutation.done = true
	return n, nil
}

// EmailConfigUpdateOne is the builder for updating a single EmailConfig entity.
type EmailConfigUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *EmailConfigMutation
}

// SetHost sets the "host" field.
func (ecuo *EmailConfigUpdateOne) SetHost(s string) *EmailConfigUpdateOne {
	ecuo.mutation.SetHost(s)
	return ecuo
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillableHost(s *string) *EmailConfigUpdateOne {
	if s != nil {
		ecuo.SetHost(*s)
	}
	return ecuo
}

// SetPort sets the "port" field.
func (ecuo *EmailConfigUpdateOne) SetPort(i int) *EmailConfigUpdateOne {
	ecuo.mutation.ResetPort()
	ecuo.mutation.SetPort(i)
	return ecuo
}

// SetNillablePort sets the "port" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillablePort(i *int) *EmailConfigUpdateOne {
	if i != nil {
		ecuo.SetPort(*i)
	}
	return ecuo
}

// AddPort adds i to the "port" field.
func (ecuo *EmailConfigUpdateOne) AddPort(i int) *EmailConfigUpdateOne {
	ecuo.mutation.AddPort(i)
	return ecuo
}

// SetUsername sets the "username" field.
func (ecuo *EmailConfigUpdateOne) SetUsername(s string) *EmailConfigUpdateOne {
	ecuo.mutation.SetUsername(s)
	return ecuo
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillableUsername(s *string) *EmailConfigUpdateOne {
	if s != nil {
		ecuo.SetUsername(*s)
	}
	return ecuo
}

// ClearUsername clears the value of the "username" field.
func (ecuo *EmailConfigUpdateOne) ClearUsername() *EmailConfigUpdateOne {
	ecuo.mutation.ClearUsername()
	return ecuo
}

// SetPassword sets the "password" field.
func (ecuo *EmailConfigUpdateOne) SetPassword(s string) *EmailConfigUpdateOne {
	ecuo.mutation.SetPassword(s)
	return ecuo
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillablePassword(s *string) *EmailConfigUpdateOne {
	if s != nil {
		ecuo.SetPassword(*s)
	}
	return ecuo
}

// ClearPassword clears the value of the "password" field.
func (ecuo *EmailConfigUpdateOne) ClearPassword() *EmailConfigUpdateOne {
	ecuo.mutation.ClearPassword()
	return ecuo
}

// SetFrom sets the "from" field.
func (ecuo *EmailConfigUpdateOne) SetFrom(s string) *EmailConfigUpdateOne {
	ecuo.mutation.SetFrom(s)
	return ecuo
}

// SetNillableFrom sets the "from" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillableFrom(s *string) *EmailConfigUpdateOne {
	if s != nil {
		ecuo.SetFrom(*s)
	}
	return ecuo
}

// SetTo sets the "to" field.
func (ecuo *EmailConfigUpdateOne) SetTo(s string) *EmailConfigUpdateOne {
	ecuo.mutation.SetTo(s)
	return ecuo
}

// SetNillableTo sets the "to" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillableTo(s *string) *EmailConfigUpdateOne {
	if s != nil {
		ecuo.SetTo(*s)
	}
	return ecuo
}

// SetEnabled sets the "enabled" field.
func (ecuo *EmailConfigUpdateOne) SetEnabled(b bool) *EmailConfigUpdateOne {
	ecuo.mutation.SetEnabled(b)
	return ecuo
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (ecuo *EmailConfigUpdateOne) SetNillableEnabled(b *bool) *EmailConfigUpdateOne {
	if b != nil {
		ecuo.SetEnabled(*b)
	}
	return ecuo
}

// Mutation returns the EmailConfigMutation object of the builder.
func (ecuo *EmailConfigUpdateOne) Mutation() *EmailConfigMutation {
	return ecuo.mutation
}

// Where appends a list predicates to the EmailConfigUpdate builder.
func (ecuo *EmailConfigUpdateOne) Where(ps ...predicate.EmailConfig) *EmailConfigUpdateOne {
	ecuo.mutation.Where(ps...)
	return ecuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ecuo *EmailConfigUpdateOne) Select(field string, fields ...string) *EmailConfigUpdateOne {
	ecuo.fields = append([]string{field}, fields...)
	return ecuo
}

// Save executes the query and returns the updated EmailConfig entity.
func (ecuo *EmailConfigUpdateOne) Save(ctx context.Context) (*EmailConfig, error) {
	return withHooks(ctx, ecuo.sqlSave, ecuo.mutation, ecuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ecuo *EmailConfigUpdateOne) SaveX(ctx context.Context) *EmailConfig {
	node, err := ecuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ecuo *EmailConfigUpdateOne) Exec(ctx context.Context) error {
	_, err := ecuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ecuo *EmailConfigUpdateOne) ExecX(ctx context.Context) {
	if err := ecuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ecuo *EmailConfigUpdateOne) check() error {
	if v, ok := ecuo.mutation.Host(); ok {
		if err := emailconfig.HostValidator(v); err != nil {
			return &ValidationError{Name: "host", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.host": %w`, err)}
		}
	}
	if v, ok := ecuo.mutation.From(); ok {
		if err := emailconfig.FromValidator(v); err != nil {
			return &ValidationError{Name: "from", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.from": %w`, err)}
		}
	}
	if v, ok := ecuo.mutation.To(); ok {
		if err := emailconfig.ToValidator(v); err != nil {
			return &ValidationError{Name: "to", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.to": %w`, err)}
		}
	}
	return nil
}

func (ecuo *EmailConfigUpdateOne) sqlSave(ctx context.Context) (_node *EmailConfig, err error) {
	if err := ecuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(emailconfig.Table, emailconfig.Columns, sqlgraph.NewFieldSpec(emailconfig.FieldID, field.TypeString))
	id, ok := ecuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "EmailConfig.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ecuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, emailconfig.FieldID)
		for _, f := range fields {
			if !emailconfig.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != emailconfig.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ecuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ecuo.mutation.Host(); ok {
		_spec.SetField(emailconfig.FieldHost, field.TypeString, value)
	}
	if value, ok := ecuo.mutation.Port(); ok {
		_spec.SetField(emailconfig.FieldPort, field.TypeInt, value)
	}
	if value, ok := ecuo.mutation.AddedPort(); ok {
		_spec.AddField(emailconfig.FieldPort, field.TypeInt, value)
	}
	if value, ok := ecuo.mutation.Username(); ok {
		_spec.SetField(emailconfig.FieldUsername, field.TypeString, value)
	}
	if ecuo.mutation.UsernameCleared() {
		_spec.ClearField(emailconfig.FieldUsername, field.TypeString)
	}
	if value, ok := ecuo.mutation.Password(); ok {
		_spec.SetField(emailconfig.FieldPassword, field.TypeString, value)
	}
	if ecuo.mutation.PasswordCleared() {
		_spec.ClearField(emailconfig.FieldPassword, field.TypeString)
	}
	if value, ok := ecuo.mutation.From(); ok {
		_spec.SetField(emailconfig.FieldFrom, field.TypeString, value)
	}
	if value, ok := ecuo.mutation.To(); ok {
		_spec.SetField(emailconfig.FieldTo, field.TypeString, value)
	}
	if value, ok := ecuo.mutation.Enabled(); ok {
		_spec.SetField(emailconfig.FieldEnabled, field.TypeBool, value)
	}
	_node = &EmailConfig{config: ecuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ecuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{emailconfig.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ecuo.mutation.done = true
	return _node, nil
}
