// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"server-monitor/ent/server"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Server is the model entity for the Server schema.
type Server struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Host holds the value of the "host" field.
	Host string `json:"host,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// Latency holds the value of the "latency" field.
	Latency float64 `json:"latency,omitempty"`
	// CheckInterval holds the value of the "check_interval" field.
	CheckInterval int `json:"check_interval,omitempty"`
	// LastDown holds the value of the "last_down" field.
	LastDown *time.Time `json:"last_down,omitempty"`
	// LastDowntimeDuration holds the value of the "last_downtime_duration" field.
	LastDowntimeDuration int64 `json:"last_downtime_duration,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ServerQuery when eager-loading is set.
	Edges        ServerEdges `json:"edges"`
	selectValues sql.SelectValues
}

// ServerEdges holds the relations/edges for other nodes in the graph.
type ServerEdges struct {
	// DowntimeIncidents holds the value of the downtime_incidents edge.
	DowntimeIncidents []*DowntimeIncident `json:"downtime_incidents,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// DowntimeIncidentsOrErr returns the DowntimeIncidents value or an error if the edge
// was not loaded in eager-loading.
func (e ServerEdges) DowntimeIncidentsOrErr() ([]*DowntimeIncident, error) {
	if e.loadedTypes[0] {
		return e.DowntimeIncidents, nil
	}
	return nil, &NotLoadedError{edge: "downtime_incidents"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Server) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case server.FieldLatency:
			values[i] = new(sql.NullFloat64)
		case server.FieldCheckInterval, server.FieldLastDowntimeDuration:
			values[i] = new(sql.NullInt64)
		case server.FieldID, server.FieldName, server.FieldHost, server.FieldStatus:
			values[i] = new(sql.NullString)
		case server.FieldLastDown, server.FieldCreatedAt, server.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Server fields.
func (s *Server) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case server.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				s.ID = value.String
			}
		case server.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				s.Name = value.String
			}
		case server.FieldHost:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field host", values[i])
			} else if value.Valid {
				s.Host = value.String
			}
		case server.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				s.Status = value.String
			}
		case server.FieldLatency:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field latency", values[i])
			} else if value.Valid {
				s.Latency = value.Float64
			}
		case server.FieldCheckInterval:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field check_interval", values[i])
			} else if value.Valid {
				s.CheckInterval = int(value.Int64)
			}
		case server.FieldLastDown:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_down", values[i])
			} else if value.Valid {
				s.LastDown = new(time.Time)
				*s.LastDown = value.Time
			}
		case server.FieldLastDowntimeDuration:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field last_downtime_duration", values[i])
			} else if value.Valid {
				s.LastDowntimeDuration = value.Int64
			}
		case server.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				s.CreatedAt = value.Time
			}
		case server.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				s.UpdatedAt = value.Time
			}
		default:
			s.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Server.
// This includes values selected through modifiers, order, etc.
func (s *Server) Value(name string) (ent.Value, error) {
	return s.selectValues.Get(name)
}

// QueryDowntimeIncidents queries the "downtime_incidents" edge of the Server entity.
func (s *Server) QueryDowntimeIncidents() *DowntimeIncidentQuery {
	return NewServerClient(s.config).QueryDowntimeIncidents(s)
}

// Update returns a builder for updating this Server.
// Note that you need to call Server.Unwrap() before calling this method if this Server
// was returned from a transaction, and the transaction was committed or rolled back.
func (s *Server) Update() *ServerUpdateOne {
	return NewServerClient(s.config).UpdateOne(s)
}

// Unwrap unwraps the Server entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (s *Server) Unwrap() *Server {
	_tx, ok := s.config.driver.(*txDriver)
	if !ok {
		panic("ent: Server is not a transactional entity")
	}
	s.config.driver = _tx.drv
	return s
}

// String implements the fmt.Stringer.
func (s *Server) String() string {
	var builder strings.Builder
	builder.WriteString("Server(")
	builder.WriteString(fmt.Sprintf("id=%v, ", s.ID))
	builder.WriteString("name=")
	builder.WriteString(s.Name)
	builder.WriteString(", ")
	builder.WriteString("host=")
	builder.WriteString(s.Host)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(s.Status)
	builder.WriteString(", ")
	builder.WriteString("latency=")
	builder.WriteString(fmt.Sprintf("%v", s.Latency))
	builder.WriteString(", ")
	builder.WriteString("check_interval=")
	builder.WriteString(fmt.Sprintf("%v", s.CheckInterval))
	builder.WriteString(", ")
	if v := s.LastDown; v != nil {
		builder.WriteString("last_down=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("last_downtime_duration=")
	builder.WriteString(fmt.Sprintf("%v", s.LastDowntimeDuration))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(s.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(s.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Servers is a parsable slice of Server.
type Servers []*Server
