package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// DowntimeIncident holds the schema definition for the DowntimeIncident entity.
type DowntimeIncident struct {
	ent.Schema
}

// Fields of the DowntimeIncident.
func (DowntimeIncident) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			Unique().
			Immutable(),
		field.String("server_id").
			NotEmpty(),
		field.Time("start_time").
			Default(time.Now),
		field.Time("end_time").
			Optional().
			Nillable(),
		field.Int64("duration").
			Optional().
			Nillable(),
		field.String("status_before").
			NotEmpty(),
		field.String("status_after").
			Optional().
			Nillable(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the DowntimeIncident.
func (DowntimeIncident) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("server", Server.Type).
			Ref("downtime_incidents").
			Field("server_id").
			Unique().
			Required(),
	}
}

// Indexes of the DowntimeIncident.
func (DowntimeIncident) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("server_id"),
		index.Fields("start_time"),
	}
}
