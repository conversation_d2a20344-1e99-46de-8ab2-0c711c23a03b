// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/predicate"
	"server-monitor/ent/server"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeDowntimeIncident = "DowntimeIncident"
	TypeEmailConfig      = "EmailConfig"
	TypeServer           = "Server"
)

// DowntimeIncidentMutation represents an operation that mutates the DowntimeIncident nodes in the graph.
type DowntimeIncidentMutation struct {
	config
	op            Op
	typ           string
	id            *string
	start_time    *time.Time
	end_time      *time.Time
	duration      *int64
	addduration   *int64
	status_before *string
	status_after  *string
	created_at    *time.Time
	updated_at    *time.Time
	clearedFields map[string]struct{}
	server        *string
	clearedserver bool
	done          bool
	oldValue      func(context.Context) (*DowntimeIncident, error)
	predicates    []predicate.DowntimeIncident
}

var _ ent.Mutation = (*DowntimeIncidentMutation)(nil)

// downtimeincidentOption allows management of the mutation configuration using functional options.
type downtimeincidentOption func(*DowntimeIncidentMutation)

// newDowntimeIncidentMutation creates new mutation for the DowntimeIncident entity.
func newDowntimeIncidentMutation(c config, op Op, opts ...downtimeincidentOption) *DowntimeIncidentMutation {
	m := &DowntimeIncidentMutation{
		config:        c,
		op:            op,
		typ:           TypeDowntimeIncident,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDowntimeIncidentID sets the ID field of the mutation.
func withDowntimeIncidentID(id string) downtimeincidentOption {
	return func(m *DowntimeIncidentMutation) {
		var (
			err   error
			once  sync.Once
			value *DowntimeIncident
		)
		m.oldValue = func(ctx context.Context) (*DowntimeIncident, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().DowntimeIncident.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDowntimeIncident sets the old DowntimeIncident of the mutation.
func withDowntimeIncident(node *DowntimeIncident) downtimeincidentOption {
	return func(m *DowntimeIncidentMutation) {
		m.oldValue = func(context.Context) (*DowntimeIncident, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DowntimeIncidentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DowntimeIncidentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of DowntimeIncident entities.
func (m *DowntimeIncidentMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DowntimeIncidentMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DowntimeIncidentMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().DowntimeIncident.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetServerID sets the "server_id" field.
func (m *DowntimeIncidentMutation) SetServerID(s string) {
	m.server = &s
}

// ServerID returns the value of the "server_id" field in the mutation.
func (m *DowntimeIncidentMutation) ServerID() (r string, exists bool) {
	v := m.server
	if v == nil {
		return
	}
	return *v, true
}

// OldServerID returns the old "server_id" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldServerID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldServerID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldServerID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldServerID: %w", err)
	}
	return oldValue.ServerID, nil
}

// ResetServerID resets all changes to the "server_id" field.
func (m *DowntimeIncidentMutation) ResetServerID() {
	m.server = nil
}

// SetStartTime sets the "start_time" field.
func (m *DowntimeIncidentMutation) SetStartTime(t time.Time) {
	m.start_time = &t
}

// StartTime returns the value of the "start_time" field in the mutation.
func (m *DowntimeIncidentMutation) StartTime() (r time.Time, exists bool) {
	v := m.start_time
	if v == nil {
		return
	}
	return *v, true
}

// OldStartTime returns the old "start_time" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldStartTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStartTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStartTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStartTime: %w", err)
	}
	return oldValue.StartTime, nil
}

// ResetStartTime resets all changes to the "start_time" field.
func (m *DowntimeIncidentMutation) ResetStartTime() {
	m.start_time = nil
}

// SetEndTime sets the "end_time" field.
func (m *DowntimeIncidentMutation) SetEndTime(t time.Time) {
	m.end_time = &t
}

// EndTime returns the value of the "end_time" field in the mutation.
func (m *DowntimeIncidentMutation) EndTime() (r time.Time, exists bool) {
	v := m.end_time
	if v == nil {
		return
	}
	return *v, true
}

// OldEndTime returns the old "end_time" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldEndTime(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEndTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEndTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEndTime: %w", err)
	}
	return oldValue.EndTime, nil
}

// ClearEndTime clears the value of the "end_time" field.
func (m *DowntimeIncidentMutation) ClearEndTime() {
	m.end_time = nil
	m.clearedFields[downtimeincident.FieldEndTime] = struct{}{}
}

// EndTimeCleared returns if the "end_time" field was cleared in this mutation.
func (m *DowntimeIncidentMutation) EndTimeCleared() bool {
	_, ok := m.clearedFields[downtimeincident.FieldEndTime]
	return ok
}

// ResetEndTime resets all changes to the "end_time" field.
func (m *DowntimeIncidentMutation) ResetEndTime() {
	m.end_time = nil
	delete(m.clearedFields, downtimeincident.FieldEndTime)
}

// SetDuration sets the "duration" field.
func (m *DowntimeIncidentMutation) SetDuration(i int64) {
	m.duration = &i
	m.addduration = nil
}

// Duration returns the value of the "duration" field in the mutation.
func (m *DowntimeIncidentMutation) Duration() (r int64, exists bool) {
	v := m.duration
	if v == nil {
		return
	}
	return *v, true
}

// OldDuration returns the old "duration" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldDuration(ctx context.Context) (v *int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDuration is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDuration requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDuration: %w", err)
	}
	return oldValue.Duration, nil
}

// AddDuration adds i to the "duration" field.
func (m *DowntimeIncidentMutation) AddDuration(i int64) {
	if m.addduration != nil {
		*m.addduration += i
	} else {
		m.addduration = &i
	}
}

// AddedDuration returns the value that was added to the "duration" field in this mutation.
func (m *DowntimeIncidentMutation) AddedDuration() (r int64, exists bool) {
	v := m.addduration
	if v == nil {
		return
	}
	return *v, true
}

// ClearDuration clears the value of the "duration" field.
func (m *DowntimeIncidentMutation) ClearDuration() {
	m.duration = nil
	m.addduration = nil
	m.clearedFields[downtimeincident.FieldDuration] = struct{}{}
}

// DurationCleared returns if the "duration" field was cleared in this mutation.
func (m *DowntimeIncidentMutation) DurationCleared() bool {
	_, ok := m.clearedFields[downtimeincident.FieldDuration]
	return ok
}

// ResetDuration resets all changes to the "duration" field.
func (m *DowntimeIncidentMutation) ResetDuration() {
	m.duration = nil
	m.addduration = nil
	delete(m.clearedFields, downtimeincident.FieldDuration)
}

// SetStatusBefore sets the "status_before" field.
func (m *DowntimeIncidentMutation) SetStatusBefore(s string) {
	m.status_before = &s
}

// StatusBefore returns the value of the "status_before" field in the mutation.
func (m *DowntimeIncidentMutation) StatusBefore() (r string, exists bool) {
	v := m.status_before
	if v == nil {
		return
	}
	return *v, true
}

// OldStatusBefore returns the old "status_before" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldStatusBefore(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatusBefore is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatusBefore requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatusBefore: %w", err)
	}
	return oldValue.StatusBefore, nil
}

// ResetStatusBefore resets all changes to the "status_before" field.
func (m *DowntimeIncidentMutation) ResetStatusBefore() {
	m.status_before = nil
}

// SetStatusAfter sets the "status_after" field.
func (m *DowntimeIncidentMutation) SetStatusAfter(s string) {
	m.status_after = &s
}

// StatusAfter returns the value of the "status_after" field in the mutation.
func (m *DowntimeIncidentMutation) StatusAfter() (r string, exists bool) {
	v := m.status_after
	if v == nil {
		return
	}
	return *v, true
}

// OldStatusAfter returns the old "status_after" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldStatusAfter(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatusAfter is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatusAfter requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatusAfter: %w", err)
	}
	return oldValue.StatusAfter, nil
}

// ClearStatusAfter clears the value of the "status_after" field.
func (m *DowntimeIncidentMutation) ClearStatusAfter() {
	m.status_after = nil
	m.clearedFields[downtimeincident.FieldStatusAfter] = struct{}{}
}

// StatusAfterCleared returns if the "status_after" field was cleared in this mutation.
func (m *DowntimeIncidentMutation) StatusAfterCleared() bool {
	_, ok := m.clearedFields[downtimeincident.FieldStatusAfter]
	return ok
}

// ResetStatusAfter resets all changes to the "status_after" field.
func (m *DowntimeIncidentMutation) ResetStatusAfter() {
	m.status_after = nil
	delete(m.clearedFields, downtimeincident.FieldStatusAfter)
}

// SetCreatedAt sets the "created_at" field.
func (m *DowntimeIncidentMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *DowntimeIncidentMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *DowntimeIncidentMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *DowntimeIncidentMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *DowntimeIncidentMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the DowntimeIncident entity.
// If the DowntimeIncident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DowntimeIncidentMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *DowntimeIncidentMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearServer clears the "server" edge to the Server entity.
func (m *DowntimeIncidentMutation) ClearServer() {
	m.clearedserver = true
	m.clearedFields[downtimeincident.FieldServerID] = struct{}{}
}

// ServerCleared reports if the "server" edge to the Server entity was cleared.
func (m *DowntimeIncidentMutation) ServerCleared() bool {
	return m.clearedserver
}

// ServerIDs returns the "server" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ServerID instead. It exists only for internal usage by the builders.
func (m *DowntimeIncidentMutation) ServerIDs() (ids []string) {
	if id := m.server; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetServer resets all changes to the "server" edge.
func (m *DowntimeIncidentMutation) ResetServer() {
	m.server = nil
	m.clearedserver = false
}

// Where appends a list predicates to the DowntimeIncidentMutation builder.
func (m *DowntimeIncidentMutation) Where(ps ...predicate.DowntimeIncident) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DowntimeIncidentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DowntimeIncidentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.DowntimeIncident, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DowntimeIncidentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DowntimeIncidentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (DowntimeIncident).
func (m *DowntimeIncidentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DowntimeIncidentMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.server != nil {
		fields = append(fields, downtimeincident.FieldServerID)
	}
	if m.start_time != nil {
		fields = append(fields, downtimeincident.FieldStartTime)
	}
	if m.end_time != nil {
		fields = append(fields, downtimeincident.FieldEndTime)
	}
	if m.duration != nil {
		fields = append(fields, downtimeincident.FieldDuration)
	}
	if m.status_before != nil {
		fields = append(fields, downtimeincident.FieldStatusBefore)
	}
	if m.status_after != nil {
		fields = append(fields, downtimeincident.FieldStatusAfter)
	}
	if m.created_at != nil {
		fields = append(fields, downtimeincident.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, downtimeincident.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DowntimeIncidentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case downtimeincident.FieldServerID:
		return m.ServerID()
	case downtimeincident.FieldStartTime:
		return m.StartTime()
	case downtimeincident.FieldEndTime:
		return m.EndTime()
	case downtimeincident.FieldDuration:
		return m.Duration()
	case downtimeincident.FieldStatusBefore:
		return m.StatusBefore()
	case downtimeincident.FieldStatusAfter:
		return m.StatusAfter()
	case downtimeincident.FieldCreatedAt:
		return m.CreatedAt()
	case downtimeincident.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DowntimeIncidentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case downtimeincident.FieldServerID:
		return m.OldServerID(ctx)
	case downtimeincident.FieldStartTime:
		return m.OldStartTime(ctx)
	case downtimeincident.FieldEndTime:
		return m.OldEndTime(ctx)
	case downtimeincident.FieldDuration:
		return m.OldDuration(ctx)
	case downtimeincident.FieldStatusBefore:
		return m.OldStatusBefore(ctx)
	case downtimeincident.FieldStatusAfter:
		return m.OldStatusAfter(ctx)
	case downtimeincident.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case downtimeincident.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown DowntimeIncident field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DowntimeIncidentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case downtimeincident.FieldServerID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetServerID(v)
		return nil
	case downtimeincident.FieldStartTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStartTime(v)
		return nil
	case downtimeincident.FieldEndTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEndTime(v)
		return nil
	case downtimeincident.FieldDuration:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDuration(v)
		return nil
	case downtimeincident.FieldStatusBefore:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatusBefore(v)
		return nil
	case downtimeincident.FieldStatusAfter:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatusAfter(v)
		return nil
	case downtimeincident.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case downtimeincident.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown DowntimeIncident field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DowntimeIncidentMutation) AddedFields() []string {
	var fields []string
	if m.addduration != nil {
		fields = append(fields, downtimeincident.FieldDuration)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DowntimeIncidentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case downtimeincident.FieldDuration:
		return m.AddedDuration()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DowntimeIncidentMutation) AddField(name string, value ent.Value) error {
	switch name {
	case downtimeincident.FieldDuration:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddDuration(v)
		return nil
	}
	return fmt.Errorf("unknown DowntimeIncident numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DowntimeIncidentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(downtimeincident.FieldEndTime) {
		fields = append(fields, downtimeincident.FieldEndTime)
	}
	if m.FieldCleared(downtimeincident.FieldDuration) {
		fields = append(fields, downtimeincident.FieldDuration)
	}
	if m.FieldCleared(downtimeincident.FieldStatusAfter) {
		fields = append(fields, downtimeincident.FieldStatusAfter)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DowntimeIncidentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DowntimeIncidentMutation) ClearField(name string) error {
	switch name {
	case downtimeincident.FieldEndTime:
		m.ClearEndTime()
		return nil
	case downtimeincident.FieldDuration:
		m.ClearDuration()
		return nil
	case downtimeincident.FieldStatusAfter:
		m.ClearStatusAfter()
		return nil
	}
	return fmt.Errorf("unknown DowntimeIncident nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DowntimeIncidentMutation) ResetField(name string) error {
	switch name {
	case downtimeincident.FieldServerID:
		m.ResetServerID()
		return nil
	case downtimeincident.FieldStartTime:
		m.ResetStartTime()
		return nil
	case downtimeincident.FieldEndTime:
		m.ResetEndTime()
		return nil
	case downtimeincident.FieldDuration:
		m.ResetDuration()
		return nil
	case downtimeincident.FieldStatusBefore:
		m.ResetStatusBefore()
		return nil
	case downtimeincident.FieldStatusAfter:
		m.ResetStatusAfter()
		return nil
	case downtimeincident.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case downtimeincident.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown DowntimeIncident field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DowntimeIncidentMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.server != nil {
		edges = append(edges, downtimeincident.EdgeServer)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DowntimeIncidentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case downtimeincident.EdgeServer:
		if id := m.server; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DowntimeIncidentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DowntimeIncidentMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DowntimeIncidentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedserver {
		edges = append(edges, downtimeincident.EdgeServer)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DowntimeIncidentMutation) EdgeCleared(name string) bool {
	switch name {
	case downtimeincident.EdgeServer:
		return m.clearedserver
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DowntimeIncidentMutation) ClearEdge(name string) error {
	switch name {
	case downtimeincident.EdgeServer:
		m.ClearServer()
		return nil
	}
	return fmt.Errorf("unknown DowntimeIncident unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DowntimeIncidentMutation) ResetEdge(name string) error {
	switch name {
	case downtimeincident.EdgeServer:
		m.ResetServer()
		return nil
	}
	return fmt.Errorf("unknown DowntimeIncident edge %s", name)
}

// EmailConfigMutation represents an operation that mutates the EmailConfig nodes in the graph.
type EmailConfigMutation struct {
	config
	op            Op
	typ           string
	id            *string
	host          *string
	port          *int
	addport       *int
	username      *string
	password      *string
	from          *string
	to            *string
	enabled       *bool
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*EmailConfig, error)
	predicates    []predicate.EmailConfig
}

var _ ent.Mutation = (*EmailConfigMutation)(nil)

// emailconfigOption allows management of the mutation configuration using functional options.
type emailconfigOption func(*EmailConfigMutation)

// newEmailConfigMutation creates new mutation for the EmailConfig entity.
func newEmailConfigMutation(c config, op Op, opts ...emailconfigOption) *EmailConfigMutation {
	m := &EmailConfigMutation{
		config:        c,
		op:            op,
		typ:           TypeEmailConfig,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEmailConfigID sets the ID field of the mutation.
func withEmailConfigID(id string) emailconfigOption {
	return func(m *EmailConfigMutation) {
		var (
			err   error
			once  sync.Once
			value *EmailConfig
		)
		m.oldValue = func(ctx context.Context) (*EmailConfig, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().EmailConfig.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEmailConfig sets the old EmailConfig of the mutation.
func withEmailConfig(node *EmailConfig) emailconfigOption {
	return func(m *EmailConfigMutation) {
		m.oldValue = func(context.Context) (*EmailConfig, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EmailConfigMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EmailConfigMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of EmailConfig entities.
func (m *EmailConfigMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EmailConfigMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EmailConfigMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().EmailConfig.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetHost sets the "host" field.
func (m *EmailConfigMutation) SetHost(s string) {
	m.host = &s
}

// Host returns the value of the "host" field in the mutation.
func (m *EmailConfigMutation) Host() (r string, exists bool) {
	v := m.host
	if v == nil {
		return
	}
	return *v, true
}

// OldHost returns the old "host" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldHost(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHost is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHost requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHost: %w", err)
	}
	return oldValue.Host, nil
}

// ResetHost resets all changes to the "host" field.
func (m *EmailConfigMutation) ResetHost() {
	m.host = nil
}

// SetPort sets the "port" field.
func (m *EmailConfigMutation) SetPort(i int) {
	m.port = &i
	m.addport = nil
}

// Port returns the value of the "port" field in the mutation.
func (m *EmailConfigMutation) Port() (r int, exists bool) {
	v := m.port
	if v == nil {
		return
	}
	return *v, true
}

// OldPort returns the old "port" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldPort(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPort is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPort requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPort: %w", err)
	}
	return oldValue.Port, nil
}

// AddPort adds i to the "port" field.
func (m *EmailConfigMutation) AddPort(i int) {
	if m.addport != nil {
		*m.addport += i
	} else {
		m.addport = &i
	}
}

// AddedPort returns the value that was added to the "port" field in this mutation.
func (m *EmailConfigMutation) AddedPort() (r int, exists bool) {
	v := m.addport
	if v == nil {
		return
	}
	return *v, true
}

// ResetPort resets all changes to the "port" field.
func (m *EmailConfigMutation) ResetPort() {
	m.port = nil
	m.addport = nil
}

// SetUsername sets the "username" field.
func (m *EmailConfigMutation) SetUsername(s string) {
	m.username = &s
}

// Username returns the value of the "username" field in the mutation.
func (m *EmailConfigMutation) Username() (r string, exists bool) {
	v := m.username
	if v == nil {
		return
	}
	return *v, true
}

// OldUsername returns the old "username" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsername: %w", err)
	}
	return oldValue.Username, nil
}

// ClearUsername clears the value of the "username" field.
func (m *EmailConfigMutation) ClearUsername() {
	m.username = nil
	m.clearedFields[emailconfig.FieldUsername] = struct{}{}
}

// UsernameCleared returns if the "username" field was cleared in this mutation.
func (m *EmailConfigMutation) UsernameCleared() bool {
	_, ok := m.clearedFields[emailconfig.FieldUsername]
	return ok
}

// ResetUsername resets all changes to the "username" field.
func (m *EmailConfigMutation) ResetUsername() {
	m.username = nil
	delete(m.clearedFields, emailconfig.FieldUsername)
}

// SetPassword sets the "password" field.
func (m *EmailConfigMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *EmailConfigMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ClearPassword clears the value of the "password" field.
func (m *EmailConfigMutation) ClearPassword() {
	m.password = nil
	m.clearedFields[emailconfig.FieldPassword] = struct{}{}
}

// PasswordCleared returns if the "password" field was cleared in this mutation.
func (m *EmailConfigMutation) PasswordCleared() bool {
	_, ok := m.clearedFields[emailconfig.FieldPassword]
	return ok
}

// ResetPassword resets all changes to the "password" field.
func (m *EmailConfigMutation) ResetPassword() {
	m.password = nil
	delete(m.clearedFields, emailconfig.FieldPassword)
}

// SetFrom sets the "from" field.
func (m *EmailConfigMutation) SetFrom(s string) {
	m.from = &s
}

// From returns the value of the "from" field in the mutation.
func (m *EmailConfigMutation) From() (r string, exists bool) {
	v := m.from
	if v == nil {
		return
	}
	return *v, true
}

// OldFrom returns the old "from" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldFrom(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFrom is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFrom requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFrom: %w", err)
	}
	return oldValue.From, nil
}

// ResetFrom resets all changes to the "from" field.
func (m *EmailConfigMutation) ResetFrom() {
	m.from = nil
}

// SetTo sets the "to" field.
func (m *EmailConfigMutation) SetTo(s string) {
	m.to = &s
}

// To returns the value of the "to" field in the mutation.
func (m *EmailConfigMutation) To() (r string, exists bool) {
	v := m.to
	if v == nil {
		return
	}
	return *v, true
}

// OldTo returns the old "to" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldTo(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTo is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTo requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTo: %w", err)
	}
	return oldValue.To, nil
}

// ResetTo resets all changes to the "to" field.
func (m *EmailConfigMutation) ResetTo() {
	m.to = nil
}

// SetEnabled sets the "enabled" field.
func (m *EmailConfigMutation) SetEnabled(b bool) {
	m.enabled = &b
}

// Enabled returns the value of the "enabled" field in the mutation.
func (m *EmailConfigMutation) Enabled() (r bool, exists bool) {
	v := m.enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEnabled returns the old "enabled" field's value of the EmailConfig entity.
// If the EmailConfig object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmailConfigMutation) OldEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnabled: %w", err)
	}
	return oldValue.Enabled, nil
}

// ResetEnabled resets all changes to the "enabled" field.
func (m *EmailConfigMutation) ResetEnabled() {
	m.enabled = nil
}

// Where appends a list predicates to the EmailConfigMutation builder.
func (m *EmailConfigMutation) Where(ps ...predicate.EmailConfig) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EmailConfigMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EmailConfigMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.EmailConfig, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EmailConfigMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EmailConfigMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (EmailConfig).
func (m *EmailConfigMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EmailConfigMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.host != nil {
		fields = append(fields, emailconfig.FieldHost)
	}
	if m.port != nil {
		fields = append(fields, emailconfig.FieldPort)
	}
	if m.username != nil {
		fields = append(fields, emailconfig.FieldUsername)
	}
	if m.password != nil {
		fields = append(fields, emailconfig.FieldPassword)
	}
	if m.from != nil {
		fields = append(fields, emailconfig.FieldFrom)
	}
	if m.to != nil {
		fields = append(fields, emailconfig.FieldTo)
	}
	if m.enabled != nil {
		fields = append(fields, emailconfig.FieldEnabled)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EmailConfigMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case emailconfig.FieldHost:
		return m.Host()
	case emailconfig.FieldPort:
		return m.Port()
	case emailconfig.FieldUsername:
		return m.Username()
	case emailconfig.FieldPassword:
		return m.Password()
	case emailconfig.FieldFrom:
		return m.From()
	case emailconfig.FieldTo:
		return m.To()
	case emailconfig.FieldEnabled:
		return m.Enabled()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EmailConfigMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case emailconfig.FieldHost:
		return m.OldHost(ctx)
	case emailconfig.FieldPort:
		return m.OldPort(ctx)
	case emailconfig.FieldUsername:
		return m.OldUsername(ctx)
	case emailconfig.FieldPassword:
		return m.OldPassword(ctx)
	case emailconfig.FieldFrom:
		return m.OldFrom(ctx)
	case emailconfig.FieldTo:
		return m.OldTo(ctx)
	case emailconfig.FieldEnabled:
		return m.OldEnabled(ctx)
	}
	return nil, fmt.Errorf("unknown EmailConfig field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EmailConfigMutation) SetField(name string, value ent.Value) error {
	switch name {
	case emailconfig.FieldHost:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHost(v)
		return nil
	case emailconfig.FieldPort:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPort(v)
		return nil
	case emailconfig.FieldUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsername(v)
		return nil
	case emailconfig.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	case emailconfig.FieldFrom:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFrom(v)
		return nil
	case emailconfig.FieldTo:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTo(v)
		return nil
	case emailconfig.FieldEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnabled(v)
		return nil
	}
	return fmt.Errorf("unknown EmailConfig field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EmailConfigMutation) AddedFields() []string {
	var fields []string
	if m.addport != nil {
		fields = append(fields, emailconfig.FieldPort)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EmailConfigMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case emailconfig.FieldPort:
		return m.AddedPort()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EmailConfigMutation) AddField(name string, value ent.Value) error {
	switch name {
	case emailconfig.FieldPort:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPort(v)
		return nil
	}
	return fmt.Errorf("unknown EmailConfig numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EmailConfigMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(emailconfig.FieldUsername) {
		fields = append(fields, emailconfig.FieldUsername)
	}
	if m.FieldCleared(emailconfig.FieldPassword) {
		fields = append(fields, emailconfig.FieldPassword)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EmailConfigMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EmailConfigMutation) ClearField(name string) error {
	switch name {
	case emailconfig.FieldUsername:
		m.ClearUsername()
		return nil
	case emailconfig.FieldPassword:
		m.ClearPassword()
		return nil
	}
	return fmt.Errorf("unknown EmailConfig nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EmailConfigMutation) ResetField(name string) error {
	switch name {
	case emailconfig.FieldHost:
		m.ResetHost()
		return nil
	case emailconfig.FieldPort:
		m.ResetPort()
		return nil
	case emailconfig.FieldUsername:
		m.ResetUsername()
		return nil
	case emailconfig.FieldPassword:
		m.ResetPassword()
		return nil
	case emailconfig.FieldFrom:
		m.ResetFrom()
		return nil
	case emailconfig.FieldTo:
		m.ResetTo()
		return nil
	case emailconfig.FieldEnabled:
		m.ResetEnabled()
		return nil
	}
	return fmt.Errorf("unknown EmailConfig field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EmailConfigMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EmailConfigMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EmailConfigMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EmailConfigMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EmailConfigMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EmailConfigMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EmailConfigMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown EmailConfig unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EmailConfigMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown EmailConfig edge %s", name)
}

// ServerMutation represents an operation that mutates the Server nodes in the graph.
type ServerMutation struct {
	config
	op                        Op
	typ                       string
	id                        *string
	name                      *string
	host                      *string
	status                    *string
	latency                   *float64
	addlatency                *float64
	check_interval            *int
	addcheck_interval         *int
	last_down                 *time.Time
	last_downtime_duration    *int64
	addlast_downtime_duration *int64
	created_at                *time.Time
	updated_at                *time.Time
	clearedFields             map[string]struct{}
	downtime_incidents        map[string]struct{}
	removeddowntime_incidents map[string]struct{}
	cleareddowntime_incidents bool
	done                      bool
	oldValue                  func(context.Context) (*Server, error)
	predicates                []predicate.Server
}

var _ ent.Mutation = (*ServerMutation)(nil)

// serverOption allows management of the mutation configuration using functional options.
type serverOption func(*ServerMutation)

// newServerMutation creates new mutation for the Server entity.
func newServerMutation(c config, op Op, opts ...serverOption) *ServerMutation {
	m := &ServerMutation{
		config:        c,
		op:            op,
		typ:           TypeServer,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withServerID sets the ID field of the mutation.
func withServerID(id string) serverOption {
	return func(m *ServerMutation) {
		var (
			err   error
			once  sync.Once
			value *Server
		)
		m.oldValue = func(ctx context.Context) (*Server, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Server.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withServer sets the old Server of the mutation.
func withServer(node *Server) serverOption {
	return func(m *ServerMutation) {
		m.oldValue = func(context.Context) (*Server, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ServerMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ServerMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Server entities.
func (m *ServerMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ServerMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ServerMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Server.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *ServerMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *ServerMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *ServerMutation) ResetName() {
	m.name = nil
}

// SetHost sets the "host" field.
func (m *ServerMutation) SetHost(s string) {
	m.host = &s
}

// Host returns the value of the "host" field in the mutation.
func (m *ServerMutation) Host() (r string, exists bool) {
	v := m.host
	if v == nil {
		return
	}
	return *v, true
}

// OldHost returns the old "host" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldHost(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHost is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHost requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHost: %w", err)
	}
	return oldValue.Host, nil
}

// ResetHost resets all changes to the "host" field.
func (m *ServerMutation) ResetHost() {
	m.host = nil
}

// SetStatus sets the "status" field.
func (m *ServerMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *ServerMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *ServerMutation) ResetStatus() {
	m.status = nil
}

// SetLatency sets the "latency" field.
func (m *ServerMutation) SetLatency(f float64) {
	m.latency = &f
	m.addlatency = nil
}

// Latency returns the value of the "latency" field in the mutation.
func (m *ServerMutation) Latency() (r float64, exists bool) {
	v := m.latency
	if v == nil {
		return
	}
	return *v, true
}

// OldLatency returns the old "latency" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldLatency(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLatency is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLatency requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLatency: %w", err)
	}
	return oldValue.Latency, nil
}

// AddLatency adds f to the "latency" field.
func (m *ServerMutation) AddLatency(f float64) {
	if m.addlatency != nil {
		*m.addlatency += f
	} else {
		m.addlatency = &f
	}
}

// AddedLatency returns the value that was added to the "latency" field in this mutation.
func (m *ServerMutation) AddedLatency() (r float64, exists bool) {
	v := m.addlatency
	if v == nil {
		return
	}
	return *v, true
}

// ResetLatency resets all changes to the "latency" field.
func (m *ServerMutation) ResetLatency() {
	m.latency = nil
	m.addlatency = nil
}

// SetCheckInterval sets the "check_interval" field.
func (m *ServerMutation) SetCheckInterval(i int) {
	m.check_interval = &i
	m.addcheck_interval = nil
}

// CheckInterval returns the value of the "check_interval" field in the mutation.
func (m *ServerMutation) CheckInterval() (r int, exists bool) {
	v := m.check_interval
	if v == nil {
		return
	}
	return *v, true
}

// OldCheckInterval returns the old "check_interval" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldCheckInterval(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCheckInterval is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCheckInterval requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCheckInterval: %w", err)
	}
	return oldValue.CheckInterval, nil
}

// AddCheckInterval adds i to the "check_interval" field.
func (m *ServerMutation) AddCheckInterval(i int) {
	if m.addcheck_interval != nil {
		*m.addcheck_interval += i
	} else {
		m.addcheck_interval = &i
	}
}

// AddedCheckInterval returns the value that was added to the "check_interval" field in this mutation.
func (m *ServerMutation) AddedCheckInterval() (r int, exists bool) {
	v := m.addcheck_interval
	if v == nil {
		return
	}
	return *v, true
}

// ResetCheckInterval resets all changes to the "check_interval" field.
func (m *ServerMutation) ResetCheckInterval() {
	m.check_interval = nil
	m.addcheck_interval = nil
}

// SetLastDown sets the "last_down" field.
func (m *ServerMutation) SetLastDown(t time.Time) {
	m.last_down = &t
}

// LastDown returns the value of the "last_down" field in the mutation.
func (m *ServerMutation) LastDown() (r time.Time, exists bool) {
	v := m.last_down
	if v == nil {
		return
	}
	return *v, true
}

// OldLastDown returns the old "last_down" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldLastDown(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastDown is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastDown requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastDown: %w", err)
	}
	return oldValue.LastDown, nil
}

// ClearLastDown clears the value of the "last_down" field.
func (m *ServerMutation) ClearLastDown() {
	m.last_down = nil
	m.clearedFields[server.FieldLastDown] = struct{}{}
}

// LastDownCleared returns if the "last_down" field was cleared in this mutation.
func (m *ServerMutation) LastDownCleared() bool {
	_, ok := m.clearedFields[server.FieldLastDown]
	return ok
}

// ResetLastDown resets all changes to the "last_down" field.
func (m *ServerMutation) ResetLastDown() {
	m.last_down = nil
	delete(m.clearedFields, server.FieldLastDown)
}

// SetLastDowntimeDuration sets the "last_downtime_duration" field.
func (m *ServerMutation) SetLastDowntimeDuration(i int64) {
	m.last_downtime_duration = &i
	m.addlast_downtime_duration = nil
}

// LastDowntimeDuration returns the value of the "last_downtime_duration" field in the mutation.
func (m *ServerMutation) LastDowntimeDuration() (r int64, exists bool) {
	v := m.last_downtime_duration
	if v == nil {
		return
	}
	return *v, true
}

// OldLastDowntimeDuration returns the old "last_downtime_duration" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldLastDowntimeDuration(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastDowntimeDuration is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastDowntimeDuration requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastDowntimeDuration: %w", err)
	}
	return oldValue.LastDowntimeDuration, nil
}

// AddLastDowntimeDuration adds i to the "last_downtime_duration" field.
func (m *ServerMutation) AddLastDowntimeDuration(i int64) {
	if m.addlast_downtime_duration != nil {
		*m.addlast_downtime_duration += i
	} else {
		m.addlast_downtime_duration = &i
	}
}

// AddedLastDowntimeDuration returns the value that was added to the "last_downtime_duration" field in this mutation.
func (m *ServerMutation) AddedLastDowntimeDuration() (r int64, exists bool) {
	v := m.addlast_downtime_duration
	if v == nil {
		return
	}
	return *v, true
}

// ResetLastDowntimeDuration resets all changes to the "last_downtime_duration" field.
func (m *ServerMutation) ResetLastDowntimeDuration() {
	m.last_downtime_duration = nil
	m.addlast_downtime_duration = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *ServerMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ServerMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ServerMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ServerMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ServerMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Server entity.
// If the Server object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ServerMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ServerMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// AddDowntimeIncidentIDs adds the "downtime_incidents" edge to the DowntimeIncident entity by ids.
func (m *ServerMutation) AddDowntimeIncidentIDs(ids ...string) {
	if m.downtime_incidents == nil {
		m.downtime_incidents = make(map[string]struct{})
	}
	for i := range ids {
		m.downtime_incidents[ids[i]] = struct{}{}
	}
}

// ClearDowntimeIncidents clears the "downtime_incidents" edge to the DowntimeIncident entity.
func (m *ServerMutation) ClearDowntimeIncidents() {
	m.cleareddowntime_incidents = true
}

// DowntimeIncidentsCleared reports if the "downtime_incidents" edge to the DowntimeIncident entity was cleared.
func (m *ServerMutation) DowntimeIncidentsCleared() bool {
	return m.cleareddowntime_incidents
}

// RemoveDowntimeIncidentIDs removes the "downtime_incidents" edge to the DowntimeIncident entity by IDs.
func (m *ServerMutation) RemoveDowntimeIncidentIDs(ids ...string) {
	if m.removeddowntime_incidents == nil {
		m.removeddowntime_incidents = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.downtime_incidents, ids[i])
		m.removeddowntime_incidents[ids[i]] = struct{}{}
	}
}

// RemovedDowntimeIncidents returns the removed IDs of the "downtime_incidents" edge to the DowntimeIncident entity.
func (m *ServerMutation) RemovedDowntimeIncidentsIDs() (ids []string) {
	for id := range m.removeddowntime_incidents {
		ids = append(ids, id)
	}
	return
}

// DowntimeIncidentsIDs returns the "downtime_incidents" edge IDs in the mutation.
func (m *ServerMutation) DowntimeIncidentsIDs() (ids []string) {
	for id := range m.downtime_incidents {
		ids = append(ids, id)
	}
	return
}

// ResetDowntimeIncidents resets all changes to the "downtime_incidents" edge.
func (m *ServerMutation) ResetDowntimeIncidents() {
	m.downtime_incidents = nil
	m.cleareddowntime_incidents = false
	m.removeddowntime_incidents = nil
}

// Where appends a list predicates to the ServerMutation builder.
func (m *ServerMutation) Where(ps ...predicate.Server) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ServerMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ServerMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Server, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ServerMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ServerMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Server).
func (m *ServerMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ServerMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.name != nil {
		fields = append(fields, server.FieldName)
	}
	if m.host != nil {
		fields = append(fields, server.FieldHost)
	}
	if m.status != nil {
		fields = append(fields, server.FieldStatus)
	}
	if m.latency != nil {
		fields = append(fields, server.FieldLatency)
	}
	if m.check_interval != nil {
		fields = append(fields, server.FieldCheckInterval)
	}
	if m.last_down != nil {
		fields = append(fields, server.FieldLastDown)
	}
	if m.last_downtime_duration != nil {
		fields = append(fields, server.FieldLastDowntimeDuration)
	}
	if m.created_at != nil {
		fields = append(fields, server.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, server.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ServerMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case server.FieldName:
		return m.Name()
	case server.FieldHost:
		return m.Host()
	case server.FieldStatus:
		return m.Status()
	case server.FieldLatency:
		return m.Latency()
	case server.FieldCheckInterval:
		return m.CheckInterval()
	case server.FieldLastDown:
		return m.LastDown()
	case server.FieldLastDowntimeDuration:
		return m.LastDowntimeDuration()
	case server.FieldCreatedAt:
		return m.CreatedAt()
	case server.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ServerMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case server.FieldName:
		return m.OldName(ctx)
	case server.FieldHost:
		return m.OldHost(ctx)
	case server.FieldStatus:
		return m.OldStatus(ctx)
	case server.FieldLatency:
		return m.OldLatency(ctx)
	case server.FieldCheckInterval:
		return m.OldCheckInterval(ctx)
	case server.FieldLastDown:
		return m.OldLastDown(ctx)
	case server.FieldLastDowntimeDuration:
		return m.OldLastDowntimeDuration(ctx)
	case server.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case server.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Server field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ServerMutation) SetField(name string, value ent.Value) error {
	switch name {
	case server.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case server.FieldHost:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHost(v)
		return nil
	case server.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case server.FieldLatency:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLatency(v)
		return nil
	case server.FieldCheckInterval:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCheckInterval(v)
		return nil
	case server.FieldLastDown:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastDown(v)
		return nil
	case server.FieldLastDowntimeDuration:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastDowntimeDuration(v)
		return nil
	case server.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case server.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Server field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ServerMutation) AddedFields() []string {
	var fields []string
	if m.addlatency != nil {
		fields = append(fields, server.FieldLatency)
	}
	if m.addcheck_interval != nil {
		fields = append(fields, server.FieldCheckInterval)
	}
	if m.addlast_downtime_duration != nil {
		fields = append(fields, server.FieldLastDowntimeDuration)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ServerMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case server.FieldLatency:
		return m.AddedLatency()
	case server.FieldCheckInterval:
		return m.AddedCheckInterval()
	case server.FieldLastDowntimeDuration:
		return m.AddedLastDowntimeDuration()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ServerMutation) AddField(name string, value ent.Value) error {
	switch name {
	case server.FieldLatency:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddLatency(v)
		return nil
	case server.FieldCheckInterval:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddCheckInterval(v)
		return nil
	case server.FieldLastDowntimeDuration:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddLastDowntimeDuration(v)
		return nil
	}
	return fmt.Errorf("unknown Server numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ServerMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(server.FieldLastDown) {
		fields = append(fields, server.FieldLastDown)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ServerMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ServerMutation) ClearField(name string) error {
	switch name {
	case server.FieldLastDown:
		m.ClearLastDown()
		return nil
	}
	return fmt.Errorf("unknown Server nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ServerMutation) ResetField(name string) error {
	switch name {
	case server.FieldName:
		m.ResetName()
		return nil
	case server.FieldHost:
		m.ResetHost()
		return nil
	case server.FieldStatus:
		m.ResetStatus()
		return nil
	case server.FieldLatency:
		m.ResetLatency()
		return nil
	case server.FieldCheckInterval:
		m.ResetCheckInterval()
		return nil
	case server.FieldLastDown:
		m.ResetLastDown()
		return nil
	case server.FieldLastDowntimeDuration:
		m.ResetLastDowntimeDuration()
		return nil
	case server.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case server.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown Server field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ServerMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.downtime_incidents != nil {
		edges = append(edges, server.EdgeDowntimeIncidents)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ServerMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case server.EdgeDowntimeIncidents:
		ids := make([]ent.Value, 0, len(m.downtime_incidents))
		for id := range m.downtime_incidents {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ServerMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removeddowntime_incidents != nil {
		edges = append(edges, server.EdgeDowntimeIncidents)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ServerMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case server.EdgeDowntimeIncidents:
		ids := make([]ent.Value, 0, len(m.removeddowntime_incidents))
		for id := range m.removeddowntime_incidents {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ServerMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cleareddowntime_incidents {
		edges = append(edges, server.EdgeDowntimeIncidents)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ServerMutation) EdgeCleared(name string) bool {
	switch name {
	case server.EdgeDowntimeIncidents:
		return m.cleareddowntime_incidents
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ServerMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Server unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ServerMutation) ResetEdge(name string) error {
	switch name {
	case server.EdgeDowntimeIncidents:
		m.ResetDowntimeIncidents()
		return nil
	}
	return fmt.Errorf("unknown Server edge %s", name)
}
