// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/predicate"
	"server-monitor/ent/server"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DowntimeIncidentQuery is the builder for querying DowntimeIncident entities.
type DowntimeIncidentQuery struct {
	config
	ctx        *QueryContext
	order      []downtimeincident.OrderOption
	inters     []Interceptor
	predicates []predicate.DowntimeIncident
	withServer *ServerQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DowntimeIncidentQuery builder.
func (diq *DowntimeIncidentQuery) Where(ps ...predicate.DowntimeIncident) *DowntimeIncidentQuery {
	diq.predicates = append(diq.predicates, ps...)
	return diq
}

// Limit the number of records to be returned by this query.
func (diq *DowntimeIncidentQuery) Limit(limit int) *DowntimeIncidentQuery {
	diq.ctx.Limit = &limit
	return diq
}

// Offset to start from.
func (diq *DowntimeIncidentQuery) Offset(offset int) *DowntimeIncidentQuery {
	diq.ctx.Offset = &offset
	return diq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (diq *DowntimeIncidentQuery) Unique(unique bool) *DowntimeIncidentQuery {
	diq.ctx.Unique = &unique
	return diq
}

// Order specifies how the records should be ordered.
func (diq *DowntimeIncidentQuery) Order(o ...downtimeincident.OrderOption) *DowntimeIncidentQuery {
	diq.order = append(diq.order, o...)
	return diq
}

// QueryServer chains the current query on the "server" edge.
func (diq *DowntimeIncidentQuery) QueryServer() *ServerQuery {
	query := (&ServerClient{config: diq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := diq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := diq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(downtimeincident.Table, downtimeincident.FieldID, selector),
			sqlgraph.To(server.Table, server.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, downtimeincident.ServerTable, downtimeincident.ServerColumn),
		)
		fromU = sqlgraph.SetNeighbors(diq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first DowntimeIncident entity from the query.
// Returns a *NotFoundError when no DowntimeIncident was found.
func (diq *DowntimeIncidentQuery) First(ctx context.Context) (*DowntimeIncident, error) {
	nodes, err := diq.Limit(1).All(setContextOp(ctx, diq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{downtimeincident.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) FirstX(ctx context.Context) *DowntimeIncident {
	node, err := diq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first DowntimeIncident ID from the query.
// Returns a *NotFoundError when no DowntimeIncident ID was found.
func (diq *DowntimeIncidentQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = diq.Limit(1).IDs(setContextOp(ctx, diq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{downtimeincident.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) FirstIDX(ctx context.Context) string {
	id, err := diq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single DowntimeIncident entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one DowntimeIncident entity is found.
// Returns a *NotFoundError when no DowntimeIncident entities are found.
func (diq *DowntimeIncidentQuery) Only(ctx context.Context) (*DowntimeIncident, error) {
	nodes, err := diq.Limit(2).All(setContextOp(ctx, diq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{downtimeincident.Label}
	default:
		return nil, &NotSingularError{downtimeincident.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) OnlyX(ctx context.Context) *DowntimeIncident {
	node, err := diq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only DowntimeIncident ID in the query.
// Returns a *NotSingularError when more than one DowntimeIncident ID is found.
// Returns a *NotFoundError when no entities are found.
func (diq *DowntimeIncidentQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = diq.Limit(2).IDs(setContextOp(ctx, diq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{downtimeincident.Label}
	default:
		err = &NotSingularError{downtimeincident.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) OnlyIDX(ctx context.Context) string {
	id, err := diq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DowntimeIncidents.
func (diq *DowntimeIncidentQuery) All(ctx context.Context) ([]*DowntimeIncident, error) {
	ctx = setContextOp(ctx, diq.ctx, ent.OpQueryAll)
	if err := diq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*DowntimeIncident, *DowntimeIncidentQuery]()
	return withInterceptors[[]*DowntimeIncident](ctx, diq, qr, diq.inters)
}

// AllX is like All, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) AllX(ctx context.Context) []*DowntimeIncident {
	nodes, err := diq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of DowntimeIncident IDs.
func (diq *DowntimeIncidentQuery) IDs(ctx context.Context) (ids []string, err error) {
	if diq.ctx.Unique == nil && diq.path != nil {
		diq.Unique(true)
	}
	ctx = setContextOp(ctx, diq.ctx, ent.OpQueryIDs)
	if err = diq.Select(downtimeincident.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) IDsX(ctx context.Context) []string {
	ids, err := diq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (diq *DowntimeIncidentQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, diq.ctx, ent.OpQueryCount)
	if err := diq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, diq, querierCount[*DowntimeIncidentQuery](), diq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) CountX(ctx context.Context) int {
	count, err := diq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (diq *DowntimeIncidentQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, diq.ctx, ent.OpQueryExist)
	switch _, err := diq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (diq *DowntimeIncidentQuery) ExistX(ctx context.Context) bool {
	exist, err := diq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DowntimeIncidentQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (diq *DowntimeIncidentQuery) Clone() *DowntimeIncidentQuery {
	if diq == nil {
		return nil
	}
	return &DowntimeIncidentQuery{
		config:     diq.config,
		ctx:        diq.ctx.Clone(),
		order:      append([]downtimeincident.OrderOption{}, diq.order...),
		inters:     append([]Interceptor{}, diq.inters...),
		predicates: append([]predicate.DowntimeIncident{}, diq.predicates...),
		withServer: diq.withServer.Clone(),
		// clone intermediate query.
		sql:  diq.sql.Clone(),
		path: diq.path,
	}
}

// WithServer tells the query-builder to eager-load the nodes that are connected to
// the "server" edge. The optional arguments are used to configure the query builder of the edge.
func (diq *DowntimeIncidentQuery) WithServer(opts ...func(*ServerQuery)) *DowntimeIncidentQuery {
	query := (&ServerClient{config: diq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	diq.withServer = query
	return diq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		ServerID string `json:"server_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.DowntimeIncident.Query().
//		GroupBy(downtimeincident.FieldServerID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (diq *DowntimeIncidentQuery) GroupBy(field string, fields ...string) *DowntimeIncidentGroupBy {
	diq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DowntimeIncidentGroupBy{build: diq}
	grbuild.flds = &diq.ctx.Fields
	grbuild.label = downtimeincident.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		ServerID string `json:"server_id,omitempty"`
//	}
//
//	client.DowntimeIncident.Query().
//		Select(downtimeincident.FieldServerID).
//		Scan(ctx, &v)
func (diq *DowntimeIncidentQuery) Select(fields ...string) *DowntimeIncidentSelect {
	diq.ctx.Fields = append(diq.ctx.Fields, fields...)
	sbuild := &DowntimeIncidentSelect{DowntimeIncidentQuery: diq}
	sbuild.label = downtimeincident.Label
	sbuild.flds, sbuild.scan = &diq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DowntimeIncidentSelect configured with the given aggregations.
func (diq *DowntimeIncidentQuery) Aggregate(fns ...AggregateFunc) *DowntimeIncidentSelect {
	return diq.Select().Aggregate(fns...)
}

func (diq *DowntimeIncidentQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range diq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, diq); err != nil {
				return err
			}
		}
	}
	for _, f := range diq.ctx.Fields {
		if !downtimeincident.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if diq.path != nil {
		prev, err := diq.path(ctx)
		if err != nil {
			return err
		}
		diq.sql = prev
	}
	return nil
}

func (diq *DowntimeIncidentQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*DowntimeIncident, error) {
	var (
		nodes       = []*DowntimeIncident{}
		_spec       = diq.querySpec()
		loadedTypes = [1]bool{
			diq.withServer != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*DowntimeIncident).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &DowntimeIncident{config: diq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, diq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := diq.withServer; query != nil {
		if err := diq.loadServer(ctx, query, nodes, nil,
			func(n *DowntimeIncident, e *Server) { n.Edges.Server = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (diq *DowntimeIncidentQuery) loadServer(ctx context.Context, query *ServerQuery, nodes []*DowntimeIncident, init func(*DowntimeIncident), assign func(*DowntimeIncident, *Server)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*DowntimeIncident)
	for i := range nodes {
		fk := nodes[i].ServerID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(server.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "server_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (diq *DowntimeIncidentQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := diq.querySpec()
	_spec.Node.Columns = diq.ctx.Fields
	if len(diq.ctx.Fields) > 0 {
		_spec.Unique = diq.ctx.Unique != nil && *diq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, diq.driver, _spec)
}

func (diq *DowntimeIncidentQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(downtimeincident.Table, downtimeincident.Columns, sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString))
	_spec.From = diq.sql
	if unique := diq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if diq.path != nil {
		_spec.Unique = true
	}
	if fields := diq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, downtimeincident.FieldID)
		for i := range fields {
			if fields[i] != downtimeincident.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if diq.withServer != nil {
			_spec.Node.AddColumnOnce(downtimeincident.FieldServerID)
		}
	}
	if ps := diq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := diq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := diq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := diq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (diq *DowntimeIncidentQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(diq.driver.Dialect())
	t1 := builder.Table(downtimeincident.Table)
	columns := diq.ctx.Fields
	if len(columns) == 0 {
		columns = downtimeincident.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if diq.sql != nil {
		selector = diq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if diq.ctx.Unique != nil && *diq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range diq.predicates {
		p(selector)
	}
	for _, p := range diq.order {
		p(selector)
	}
	if offset := diq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := diq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// DowntimeIncidentGroupBy is the group-by builder for DowntimeIncident entities.
type DowntimeIncidentGroupBy struct {
	selector
	build *DowntimeIncidentQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (digb *DowntimeIncidentGroupBy) Aggregate(fns ...AggregateFunc) *DowntimeIncidentGroupBy {
	digb.fns = append(digb.fns, fns...)
	return digb
}

// Scan applies the selector query and scans the result into the given value.
func (digb *DowntimeIncidentGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, digb.build.ctx, ent.OpQueryGroupBy)
	if err := digb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DowntimeIncidentQuery, *DowntimeIncidentGroupBy](ctx, digb.build, digb, digb.build.inters, v)
}

func (digb *DowntimeIncidentGroupBy) sqlScan(ctx context.Context, root *DowntimeIncidentQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(digb.fns))
	for _, fn := range digb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*digb.flds)+len(digb.fns))
		for _, f := range *digb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*digb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := digb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DowntimeIncidentSelect is the builder for selecting fields of DowntimeIncident entities.
type DowntimeIncidentSelect struct {
	*DowntimeIncidentQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (dis *DowntimeIncidentSelect) Aggregate(fns ...AggregateFunc) *DowntimeIncidentSelect {
	dis.fns = append(dis.fns, fns...)
	return dis
}

// Scan applies the selector query and scans the result into the given value.
func (dis *DowntimeIncidentSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dis.ctx, ent.OpQuerySelect)
	if err := dis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DowntimeIncidentQuery, *DowntimeIncidentSelect](ctx, dis.DowntimeIncidentQuery, dis, dis.inters, v)
}

func (dis *DowntimeIncidentSelect) sqlScan(ctx context.Context, root *DowntimeIncidentQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(dis.fns))
	for _, fn := range dis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*dis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
