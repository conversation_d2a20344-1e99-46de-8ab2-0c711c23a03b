// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// DowntimeIncident is the predicate function for downtimeincident builders.
type DowntimeIncident func(*sql.Selector)

// EmailConfig is the predicate function for emailconfig builders.
type EmailConfig func(*sql.Selector)

// Server is the predicate function for server builders.
type Server func(*sql.Selector)
