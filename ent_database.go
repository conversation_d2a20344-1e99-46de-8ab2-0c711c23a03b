package main

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"server-monitor/ent"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/server"

	"entgo.io/ent/dialect"
	entsql "entgo.io/ent/dialect/sql"
	_ "modernc.org/sqlite" // Pure Go SQLite driver
)

// EntDatabase implements DatabaseInterface using ent ORM
type EntDatabase struct {
	client *ent.Client
}

// NewEntDatabase creates a new ent database connection
func NewEntDatabase(dbPath string) (*EntDatabase, error) {
	// Open database connection using modernc.org/sqlite
	db, err := sql.Open("sqlite", dbPath+"?_pragma=foreign_keys(1)")
	if err != nil {
		return nil, fmt.Errorf("failed opening connection to sqlite: %v", err)
	}

	// Create ent client with the database connection
	drv := entsql.OpenDB(dialect.SQLite, db)
	client := ent.NewClient(ent.Driver(drv))

	// Run the auto migration tool
	if err := client.Schema.Create(context.Background()); err != nil {
		return nil, fmt.Errorf("failed creating schema resources: %v", err)
	}

	return &EntDatabase{client: client}, nil
}

// Close closes the database connection
func (d *EntDatabase) Close() error {
	return d.client.Close()
}

// SaveServer saves a server to the database
func (d *EntDatabase) SaveServer(srv *Server) error {
	ctx := context.Background()

	// Check if server exists
	exists, err := d.client.Server.Query().Where(server.IDEQ(srv.ID)).Exist(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if server exists: %w", err)
	}

	if exists {
		// Update existing server
		_, err = d.client.Server.UpdateOneID(srv.ID).
			SetName(srv.Name).
			SetHost(srv.Host).
			SetStatus(srv.Status).
			SetLatency(srv.Latency).
			SetCheckInterval(srv.CheckInterval).
			SetNillableLastDown(srv.LastDown).
			SetLastDowntimeDuration(srv.LastDowntimeDuration).
			SetUpdatedAt(time.Now()).
			Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to update server: %w", err)
		}
	} else {
		// Create new server
		_, err = d.client.Server.Create().
			SetID(srv.ID).
			SetName(srv.Name).
			SetHost(srv.Host).
			SetStatus(srv.Status).
			SetLatency(srv.Latency).
			SetCheckInterval(srv.CheckInterval).
			SetNillableLastDown(srv.LastDown).
			SetLastDowntimeDuration(srv.LastDowntimeDuration).
			SetCreatedAt(srv.CreatedAt).
			SetUpdatedAt(time.Now()).
			Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to create server: %w", err)
		}
	}

	return nil
}

// GetServer retrieves a server by ID
func (d *EntDatabase) GetServer(id string) (*Server, error) {
	ctx := context.Background()

	entServer, err := d.client.Server.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get server: %w", err)
	}

	return d.convertEntServerToServer(entServer), nil
}

// GetAllServers retrieves all servers
func (d *EntDatabase) GetAllServers() ([]*Server, error) {
	ctx := context.Background()

	entServers, err := d.client.Server.Query().
		Order(ent.Desc(server.FieldCreatedAt)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all servers: %w", err)
	}

	servers := make([]*Server, len(entServers))
	for i, entServer := range entServers {
		servers[i] = d.convertEntServerToServer(entServer)
	}

	return servers, nil
}

// DeleteServer removes a server by ID
func (d *EntDatabase) DeleteServer(id string) error {
	ctx := context.Background()

	err := d.client.Server.DeleteOneID(id).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("server not found")
		}
		return fmt.Errorf("failed to delete server: %w", err)
	}

	return nil
}

// SaveDowntimeIncident saves a downtime incident to the database
func (d *EntDatabase) SaveDowntimeIncident(incident *DowntimeIncident) error {
	ctx := context.Background()

	// Check if incident exists
	exists, err := d.client.DowntimeIncident.Query().Where(downtimeincident.ID(incident.ID)).Exist(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if incident exists: %w", err)
	}

	if exists {
		// Update existing incident
		_, err = d.client.DowntimeIncident.UpdateOneID(incident.ID).
			SetServerID(incident.ServerID).
			SetStartTime(incident.StartTime).
			SetNillableEndTime(incident.EndTime).
			SetNillableDuration(incident.Duration).
			SetStatusBefore(incident.StatusBefore).
			SetNillableStatusAfter(incident.StatusAfter).
			SetUpdatedAt(time.Now()).
			Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to update incident: %w", err)
		}
	} else {
		// Create new incident
		_, err = d.client.DowntimeIncident.Create().
			SetID(incident.ID).
			SetServerID(incident.ServerID).
			SetStartTime(incident.StartTime).
			SetNillableEndTime(incident.EndTime).
			SetNillableDuration(incident.Duration).
			SetStatusBefore(incident.StatusBefore).
			SetNillableStatusAfter(incident.StatusAfter).
			SetCreatedAt(incident.CreatedAt).
			SetUpdatedAt(time.Now()).
			Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to create incident: %w", err)
		}
	}

	return nil
}

// GetDowntimeIncidents retrieves downtime incidents for a server with optional filtering
func (d *EntDatabase) GetDowntimeIncidents(serverID string, limit int, offset int, startDate *time.Time, endDate *time.Time) ([]*DowntimeIncident, error) {
	ctx := context.Background()

	query := d.client.DowntimeIncident.Query().
		Where(downtimeincident.ServerID(serverID))

	if startDate != nil {
		query = query.Where(downtimeincident.StartTimeGTE(*startDate))
	}

	if endDate != nil {
		query = query.Where(downtimeincident.StartTimeLTE(*endDate))
	}

	query = query.Order(ent.Desc(downtimeincident.FieldStartTime))

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	entIncidents, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get downtime incidents: %w", err)
	}

	incidents := make([]*DowntimeIncident, len(entIncidents))
	for i, entIncident := range entIncidents {
		incidents[i] = d.convertEntIncidentToIncident(entIncident)
	}

	return incidents, nil
}

// GetOngoingDowntimeIncident gets the ongoing downtime incident for a server (if any)
func (d *EntDatabase) GetOngoingDowntimeIncident(serverID string) (*DowntimeIncident, error) {
	ctx := context.Background()

	entIncident, err := d.client.DowntimeIncident.Query().
		Where(
			downtimeincident.ServerID(serverID),
			downtimeincident.EndTimeIsNil(),
		).
		Order(ent.Desc(downtimeincident.FieldStartTime)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil // No ongoing incident found
		}
		return nil, fmt.Errorf("failed to get ongoing downtime incident: %w", err)
	}

	return d.convertEntIncidentToIncident(entIncident), nil
}

// convertEntServerToServer converts ent.Server to Server
func (d *EntDatabase) convertEntServerToServer(entServer *ent.Server) *Server {
	return &Server{
		ID:                   entServer.ID,
		Name:                 entServer.Name,
		Host:                 entServer.Host,
		Status:               entServer.Status,
		Latency:              entServer.Latency,
		CheckInterval:        entServer.CheckInterval,
		LastDown:             entServer.LastDown,
		LastDowntimeDuration: entServer.LastDowntimeDuration,
		CreatedAt:            entServer.CreatedAt,
		UpdatedAt:            entServer.UpdatedAt,
	}
}

// GetDowntimeStats calculates downtime statistics for a server
func (d *EntDatabase) GetDowntimeStats(serverID string, days int) (*DowntimeStats, error) {
	ctx := context.Background()

	// Calculate the start date for the statistics period
	startDate := time.Now().AddDate(0, 0, -days)

	// Get total incidents and downtime
	incidents, err := d.client.DowntimeIncident.Query().
		Where(
			downtimeincident.ServerID(serverID),
			downtimeincident.StartTimeGTE(startDate),
		).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get downtime incidents for stats: %w", err)
	}

	totalIncidents := len(incidents)
	var totalDowntimeSeconds int64
	var lastIncidentDate *time.Time

	for _, incident := range incidents {
		if incident.Duration != nil {
			totalDowntimeSeconds += *incident.Duration
		}
		if lastIncidentDate == nil || incident.StartTime.After(*lastIncidentDate) {
			lastIncidentDate = &incident.StartTime
		}
	}

	// Calculate statistics
	totalDowntimeHours := float64(totalDowntimeSeconds) / 3600.0
	var averageIncidentHours float64
	if totalIncidents > 0 {
		averageIncidentHours = totalDowntimeHours / float64(totalIncidents)
	}

	// Calculate uptime percentage (assuming 24/7 monitoring)
	totalPeriodHours := float64(days * 24)
	uptimePercentage := ((totalPeriodHours - totalDowntimeHours) / totalPeriodHours) * 100
	if uptimePercentage < 0 {
		uptimePercentage = 0
	}

	return &DowntimeStats{
		TotalIncidents:       totalIncidents,
		TotalDowntimeHours:   totalDowntimeHours,
		AverageIncidentHours: averageIncidentHours,
		UptimePercentage:     uptimePercentage,
		LastIncidentDate:     lastIncidentDate,
	}, nil
}

// SaveEmailConfig saves email configuration to the database
func (d *EntDatabase) SaveEmailConfig(config *EmailConfig) error {
	ctx := context.Background()

	// If ID is empty, generate a new one
	if config.ID == "" {
		config.ID = "email_config_1" // We only need one email config
	}

	// Check if config exists
	exists, err := d.client.EmailConfig.Query().Where(emailconfig.IDEQ(config.ID)).Exist(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if email config exists: %w", err)
	}

	if exists {
		// Update existing config
		_, err = d.client.EmailConfig.UpdateOneID(config.ID).
			SetHost(config.Host).
			SetPort(config.Port).
			SetUsername(config.Username).
			SetPassword(config.Password).
			SetFrom(config.From).
			SetTo(config.To).
			SetEnabled(config.Enabled).
			Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to update email config: %w", err)
		}
	} else {
		// Create new config
		_, err = d.client.EmailConfig.Create().
			SetID(config.ID).
			SetHost(config.Host).
			SetPort(config.Port).
			SetUsername(config.Username).
			SetPassword(config.Password).
			SetFrom(config.From).
			SetTo(config.To).
			SetEnabled(config.Enabled).
			Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to create email config: %w", err)
		}
	}

	return nil
}

// GetEmailConfig retrieves email configuration from the database
func (d *EntDatabase) GetEmailConfig() (*EmailConfig, error) {
	ctx := context.Background()

	entConfig, err := d.client.EmailConfig.Query().First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// Return default config if not found
			return &EmailConfig{
				ID:      "email_config_1",
				Host:    "",
				Port:    587,
				Enabled: false,
			}, nil
		}
		return nil, fmt.Errorf("failed to get email config: %w", err)
	}

	return d.convertEntEmailConfigToEmailConfig(entConfig), nil
}

// convertEntIncidentToIncident converts ent.DowntimeIncident to DowntimeIncident
func (d *EntDatabase) convertEntIncidentToIncident(entIncident *ent.DowntimeIncident) *DowntimeIncident {
	return &DowntimeIncident{
		ID:           entIncident.ID,
		ServerID:     entIncident.ServerID,
		StartTime:    entIncident.StartTime,
		EndTime:      entIncident.EndTime,
		Duration:     entIncident.Duration,
		StatusBefore: entIncident.StatusBefore,
		StatusAfter:  entIncident.StatusAfter,
		CreatedAt:    entIncident.CreatedAt,
		UpdatedAt:    entIncident.UpdatedAt,
	}
}

// convertEntEmailConfigToEmailConfig converts ent.EmailConfig to EmailConfig
func (d *EntDatabase) convertEntEmailConfigToEmailConfig(entConfig *ent.EmailConfig) *EmailConfig {
	return &EmailConfig{
		ID:       entConfig.ID,
		Host:     entConfig.Host,
		Port:     entConfig.Port,
		Username: entConfig.Username,
		Password: entConfig.Password,
		From:     entConfig.From,
		To:       entConfig.To,
		Enabled:  entConfig.Enabled,
	}
}
