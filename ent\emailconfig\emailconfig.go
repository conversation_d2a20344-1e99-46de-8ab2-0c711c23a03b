// Code generated by ent, DO NOT EDIT.

package emailconfig

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the emailconfig type in the database.
	Label = "email_config"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldHost holds the string denoting the host field in the database.
	FieldHost = "host"
	// FieldPort holds the string denoting the port field in the database.
	FieldPort = "port"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldFrom holds the string denoting the from field in the database.
	FieldFrom = "from"
	// FieldTo holds the string denoting the to field in the database.
	FieldTo = "to"
	// FieldEnabled holds the string denoting the enabled field in the database.
	FieldEnabled = "enabled"
	// Table holds the table name of the emailconfig in the database.
	Table = "email_configs"
)

// Columns holds all SQL columns for emailconfig fields.
var Columns = []string{
	FieldID,
	FieldHost,
	FieldPort,
	FieldUsername,
	FieldPassword,
	FieldFrom,
	FieldTo,
	FieldEnabled,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// HostValidator is a validator for the "host" field. It is called by the builders before save.
	HostValidator func(string) error
	// DefaultPort holds the default value on creation for the "port" field.
	DefaultPort int
	// FromValidator is a validator for the "from" field. It is called by the builders before save.
	FromValidator func(string) error
	// ToValidator is a validator for the "to" field. It is called by the builders before save.
	ToValidator func(string) error
	// DefaultEnabled holds the default value on creation for the "enabled" field.
	DefaultEnabled bool
)

// OrderOption defines the ordering options for the EmailConfig queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByHost orders the results by the host field.
func ByHost(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHost, opts...).ToFunc()
}

// ByPort orders the results by the port field.
func ByPort(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPort, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByFrom orders the results by the from field.
func ByFrom(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFrom, opts...).ToFunc()
}

// ByTo orders the results by the to field.
func ByTo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTo, opts...).ToFunc()
}

// ByEnabled orders the results by the enabled field.
func ByEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnabled, opts...).ToFunc()
}
