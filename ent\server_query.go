// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/predicate"
	"server-monitor/ent/server"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ServerQuery is the builder for querying Server entities.
type ServerQuery struct {
	config
	ctx                   *QueryContext
	order                 []server.OrderOption
	inters                []Interceptor
	predicates            []predicate.Server
	withDowntimeIncidents *DowntimeIncidentQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ServerQuery builder.
func (sq *ServerQuery) Where(ps ...predicate.Server) *ServerQuery {
	sq.predicates = append(sq.predicates, ps...)
	return sq
}

// Limit the number of records to be returned by this query.
func (sq *ServerQuery) Limit(limit int) *ServerQuery {
	sq.ctx.Limit = &limit
	return sq
}

// Offset to start from.
func (sq *ServerQuery) Offset(offset int) *ServerQuery {
	sq.ctx.Offset = &offset
	return sq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (sq *ServerQuery) Unique(unique bool) *ServerQuery {
	sq.ctx.Unique = &unique
	return sq
}

// Order specifies how the records should be ordered.
func (sq *ServerQuery) Order(o ...server.OrderOption) *ServerQuery {
	sq.order = append(sq.order, o...)
	return sq
}

// QueryDowntimeIncidents chains the current query on the "downtime_incidents" edge.
func (sq *ServerQuery) QueryDowntimeIncidents() *DowntimeIncidentQuery {
	query := (&DowntimeIncidentClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(server.Table, server.FieldID, selector),
			sqlgraph.To(downtimeincident.Table, downtimeincident.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, server.DowntimeIncidentsTable, server.DowntimeIncidentsColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Server entity from the query.
// Returns a *NotFoundError when no Server was found.
func (sq *ServerQuery) First(ctx context.Context) (*Server, error) {
	nodes, err := sq.Limit(1).All(setContextOp(ctx, sq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{server.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (sq *ServerQuery) FirstX(ctx context.Context) *Server {
	node, err := sq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Server ID from the query.
// Returns a *NotFoundError when no Server ID was found.
func (sq *ServerQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = sq.Limit(1).IDs(setContextOp(ctx, sq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{server.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (sq *ServerQuery) FirstIDX(ctx context.Context) string {
	id, err := sq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Server entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Server entity is found.
// Returns a *NotFoundError when no Server entities are found.
func (sq *ServerQuery) Only(ctx context.Context) (*Server, error) {
	nodes, err := sq.Limit(2).All(setContextOp(ctx, sq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{server.Label}
	default:
		return nil, &NotSingularError{server.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (sq *ServerQuery) OnlyX(ctx context.Context) *Server {
	node, err := sq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Server ID in the query.
// Returns a *NotSingularError when more than one Server ID is found.
// Returns a *NotFoundError when no entities are found.
func (sq *ServerQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = sq.Limit(2).IDs(setContextOp(ctx, sq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{server.Label}
	default:
		err = &NotSingularError{server.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (sq *ServerQuery) OnlyIDX(ctx context.Context) string {
	id, err := sq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Servers.
func (sq *ServerQuery) All(ctx context.Context) ([]*Server, error) {
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryAll)
	if err := sq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Server, *ServerQuery]()
	return withInterceptors[[]*Server](ctx, sq, qr, sq.inters)
}

// AllX is like All, but panics if an error occurs.
func (sq *ServerQuery) AllX(ctx context.Context) []*Server {
	nodes, err := sq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Server IDs.
func (sq *ServerQuery) IDs(ctx context.Context) (ids []string, err error) {
	if sq.ctx.Unique == nil && sq.path != nil {
		sq.Unique(true)
	}
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryIDs)
	if err = sq.Select(server.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (sq *ServerQuery) IDsX(ctx context.Context) []string {
	ids, err := sq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (sq *ServerQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryCount)
	if err := sq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, sq, querierCount[*ServerQuery](), sq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (sq *ServerQuery) CountX(ctx context.Context) int {
	count, err := sq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (sq *ServerQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryExist)
	switch _, err := sq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (sq *ServerQuery) ExistX(ctx context.Context) bool {
	exist, err := sq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ServerQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (sq *ServerQuery) Clone() *ServerQuery {
	if sq == nil {
		return nil
	}
	return &ServerQuery{
		config:                sq.config,
		ctx:                   sq.ctx.Clone(),
		order:                 append([]server.OrderOption{}, sq.order...),
		inters:                append([]Interceptor{}, sq.inters...),
		predicates:            append([]predicate.Server{}, sq.predicates...),
		withDowntimeIncidents: sq.withDowntimeIncidents.Clone(),
		// clone intermediate query.
		sql:  sq.sql.Clone(),
		path: sq.path,
	}
}

// WithDowntimeIncidents tells the query-builder to eager-load the nodes that are connected to
// the "downtime_incidents" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *ServerQuery) WithDowntimeIncidents(opts ...func(*DowntimeIncidentQuery)) *ServerQuery {
	query := (&DowntimeIncidentClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withDowntimeIncidents = query
	return sq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Server.Query().
//		GroupBy(server.FieldName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (sq *ServerQuery) GroupBy(field string, fields ...string) *ServerGroupBy {
	sq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ServerGroupBy{build: sq}
	grbuild.flds = &sq.ctx.Fields
	grbuild.label = server.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//	}
//
//	client.Server.Query().
//		Select(server.FieldName).
//		Scan(ctx, &v)
func (sq *ServerQuery) Select(fields ...string) *ServerSelect {
	sq.ctx.Fields = append(sq.ctx.Fields, fields...)
	sbuild := &ServerSelect{ServerQuery: sq}
	sbuild.label = server.Label
	sbuild.flds, sbuild.scan = &sq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ServerSelect configured with the given aggregations.
func (sq *ServerQuery) Aggregate(fns ...AggregateFunc) *ServerSelect {
	return sq.Select().Aggregate(fns...)
}

func (sq *ServerQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range sq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, sq); err != nil {
				return err
			}
		}
	}
	for _, f := range sq.ctx.Fields {
		if !server.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if sq.path != nil {
		prev, err := sq.path(ctx)
		if err != nil {
			return err
		}
		sq.sql = prev
	}
	return nil
}

func (sq *ServerQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Server, error) {
	var (
		nodes       = []*Server{}
		_spec       = sq.querySpec()
		loadedTypes = [1]bool{
			sq.withDowntimeIncidents != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Server).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Server{config: sq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, sq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := sq.withDowntimeIncidents; query != nil {
		if err := sq.loadDowntimeIncidents(ctx, query, nodes,
			func(n *Server) { n.Edges.DowntimeIncidents = []*DowntimeIncident{} },
			func(n *Server, e *DowntimeIncident) { n.Edges.DowntimeIncidents = append(n.Edges.DowntimeIncidents, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (sq *ServerQuery) loadDowntimeIncidents(ctx context.Context, query *DowntimeIncidentQuery, nodes []*Server, init func(*Server), assign func(*Server, *DowntimeIncident)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[string]*Server)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(downtimeincident.FieldServerID)
	}
	query.Where(predicate.DowntimeIncident(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(server.DowntimeIncidentsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ServerID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "server_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (sq *ServerQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := sq.querySpec()
	_spec.Node.Columns = sq.ctx.Fields
	if len(sq.ctx.Fields) > 0 {
		_spec.Unique = sq.ctx.Unique != nil && *sq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, sq.driver, _spec)
}

func (sq *ServerQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(server.Table, server.Columns, sqlgraph.NewFieldSpec(server.FieldID, field.TypeString))
	_spec.From = sq.sql
	if unique := sq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if sq.path != nil {
		_spec.Unique = true
	}
	if fields := sq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, server.FieldID)
		for i := range fields {
			if fields[i] != server.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := sq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := sq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := sq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := sq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (sq *ServerQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(sq.driver.Dialect())
	t1 := builder.Table(server.Table)
	columns := sq.ctx.Fields
	if len(columns) == 0 {
		columns = server.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if sq.sql != nil {
		selector = sq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if sq.ctx.Unique != nil && *sq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range sq.predicates {
		p(selector)
	}
	for _, p := range sq.order {
		p(selector)
	}
	if offset := sq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := sq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ServerGroupBy is the group-by builder for Server entities.
type ServerGroupBy struct {
	selector
	build *ServerQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sgb *ServerGroupBy) Aggregate(fns ...AggregateFunc) *ServerGroupBy {
	sgb.fns = append(sgb.fns, fns...)
	return sgb
}

// Scan applies the selector query and scans the result into the given value.
func (sgb *ServerGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sgb.build.ctx, ent.OpQueryGroupBy)
	if err := sgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ServerQuery, *ServerGroupBy](ctx, sgb.build, sgb, sgb.build.inters, v)
}

func (sgb *ServerGroupBy) sqlScan(ctx context.Context, root *ServerQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sgb.fns))
	for _, fn := range sgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sgb.flds)+len(sgb.fns))
		for _, f := range *sgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ServerSelect is the builder for selecting fields of Server entities.
type ServerSelect struct {
	*ServerQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ss *ServerSelect) Aggregate(fns ...AggregateFunc) *ServerSelect {
	ss.fns = append(ss.fns, fns...)
	return ss
}

// Scan applies the selector query and scans the result into the given value.
func (ss *ServerSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ss.ctx, ent.OpQuerySelect)
	if err := ss.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ServerQuery, *ServerSelect](ctx, ss.ServerQuery, ss, ss.inters, v)
}

func (ss *ServerSelect) sqlScan(ctx context.Context, root *ServerQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ss.fns))
	for _, fn := range ss.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ss.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ss.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
