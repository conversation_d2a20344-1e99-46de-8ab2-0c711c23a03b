package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// EmailConfig holds the schema definition for the EmailConfig entity.
type EmailConfig struct {
	ent.Schema
}

// Fields of the EmailConfig.
func (EmailConfig) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			Unique().
			Immutable(),
		field.String("host").
			NotEmpty(),
		field.Int("port").
			Default(587),
		field.String("username").
			Optional(),
		field.String("password").
			Optional(),
		field.String("from").
			NotEmpty(),
		field.String("to").
			NotEmpty(),
		field.Bool("enabled").
			Default(false),
	}
}

// Edges of the EmailConfig.
func (EmailConfig) Edges() []ent.Edge {
	return nil
}
