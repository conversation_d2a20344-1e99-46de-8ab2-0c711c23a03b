// Code generated by ent, DO NOT EDIT.

package server

import (
	"server-monitor/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Server {
	return predicate.Server(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Server {
	return predicate.Server(sql.FieldContainsFold(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldName, v))
}

// Host applies equality check predicate on the "host" field. It's identical to HostEQ.
func Host(v string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldHost, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldStatus, v))
}

// Latency applies equality check predicate on the "latency" field. It's identical to LatencyEQ.
func Latency(v float64) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldLatency, v))
}

// CheckInterval applies equality check predicate on the "check_interval" field. It's identical to CheckIntervalEQ.
func CheckInterval(v int) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldCheckInterval, v))
}

// LastDown applies equality check predicate on the "last_down" field. It's identical to LastDownEQ.
func LastDown(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldLastDown, v))
}

// LastDowntimeDuration applies equality check predicate on the "last_downtime_duration" field. It's identical to LastDowntimeDurationEQ.
func LastDowntimeDuration(v int64) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldLastDowntimeDuration, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldUpdatedAt, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Server {
	return predicate.Server(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Server {
	return predicate.Server(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Server {
	return predicate.Server(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Server {
	return predicate.Server(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Server {
	return predicate.Server(sql.FieldContainsFold(FieldName, v))
}

// HostEQ applies the EQ predicate on the "host" field.
func HostEQ(v string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldHost, v))
}

// HostNEQ applies the NEQ predicate on the "host" field.
func HostNEQ(v string) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldHost, v))
}

// HostIn applies the In predicate on the "host" field.
func HostIn(vs ...string) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldHost, vs...))
}

// HostNotIn applies the NotIn predicate on the "host" field.
func HostNotIn(vs ...string) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldHost, vs...))
}

// HostGT applies the GT predicate on the "host" field.
func HostGT(v string) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldHost, v))
}

// HostGTE applies the GTE predicate on the "host" field.
func HostGTE(v string) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldHost, v))
}

// HostLT applies the LT predicate on the "host" field.
func HostLT(v string) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldHost, v))
}

// HostLTE applies the LTE predicate on the "host" field.
func HostLTE(v string) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldHost, v))
}

// HostContains applies the Contains predicate on the "host" field.
func HostContains(v string) predicate.Server {
	return predicate.Server(sql.FieldContains(FieldHost, v))
}

// HostHasPrefix applies the HasPrefix predicate on the "host" field.
func HostHasPrefix(v string) predicate.Server {
	return predicate.Server(sql.FieldHasPrefix(FieldHost, v))
}

// HostHasSuffix applies the HasSuffix predicate on the "host" field.
func HostHasSuffix(v string) predicate.Server {
	return predicate.Server(sql.FieldHasSuffix(FieldHost, v))
}

// HostEqualFold applies the EqualFold predicate on the "host" field.
func HostEqualFold(v string) predicate.Server {
	return predicate.Server(sql.FieldEqualFold(FieldHost, v))
}

// HostContainsFold applies the ContainsFold predicate on the "host" field.
func HostContainsFold(v string) predicate.Server {
	return predicate.Server(sql.FieldContainsFold(FieldHost, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Server {
	return predicate.Server(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Server {
	return predicate.Server(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Server {
	return predicate.Server(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Server {
	return predicate.Server(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Server {
	return predicate.Server(sql.FieldContainsFold(FieldStatus, v))
}

// LatencyEQ applies the EQ predicate on the "latency" field.
func LatencyEQ(v float64) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldLatency, v))
}

// LatencyNEQ applies the NEQ predicate on the "latency" field.
func LatencyNEQ(v float64) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldLatency, v))
}

// LatencyIn applies the In predicate on the "latency" field.
func LatencyIn(vs ...float64) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldLatency, vs...))
}

// LatencyNotIn applies the NotIn predicate on the "latency" field.
func LatencyNotIn(vs ...float64) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldLatency, vs...))
}

// LatencyGT applies the GT predicate on the "latency" field.
func LatencyGT(v float64) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldLatency, v))
}

// LatencyGTE applies the GTE predicate on the "latency" field.
func LatencyGTE(v float64) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldLatency, v))
}

// LatencyLT applies the LT predicate on the "latency" field.
func LatencyLT(v float64) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldLatency, v))
}

// LatencyLTE applies the LTE predicate on the "latency" field.
func LatencyLTE(v float64) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldLatency, v))
}

// CheckIntervalEQ applies the EQ predicate on the "check_interval" field.
func CheckIntervalEQ(v int) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldCheckInterval, v))
}

// CheckIntervalNEQ applies the NEQ predicate on the "check_interval" field.
func CheckIntervalNEQ(v int) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldCheckInterval, v))
}

// CheckIntervalIn applies the In predicate on the "check_interval" field.
func CheckIntervalIn(vs ...int) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldCheckInterval, vs...))
}

// CheckIntervalNotIn applies the NotIn predicate on the "check_interval" field.
func CheckIntervalNotIn(vs ...int) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldCheckInterval, vs...))
}

// CheckIntervalGT applies the GT predicate on the "check_interval" field.
func CheckIntervalGT(v int) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldCheckInterval, v))
}

// CheckIntervalGTE applies the GTE predicate on the "check_interval" field.
func CheckIntervalGTE(v int) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldCheckInterval, v))
}

// CheckIntervalLT applies the LT predicate on the "check_interval" field.
func CheckIntervalLT(v int) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldCheckInterval, v))
}

// CheckIntervalLTE applies the LTE predicate on the "check_interval" field.
func CheckIntervalLTE(v int) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldCheckInterval, v))
}

// LastDownEQ applies the EQ predicate on the "last_down" field.
func LastDownEQ(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldLastDown, v))
}

// LastDownNEQ applies the NEQ predicate on the "last_down" field.
func LastDownNEQ(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldLastDown, v))
}

// LastDownIn applies the In predicate on the "last_down" field.
func LastDownIn(vs ...time.Time) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldLastDown, vs...))
}

// LastDownNotIn applies the NotIn predicate on the "last_down" field.
func LastDownNotIn(vs ...time.Time) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldLastDown, vs...))
}

// LastDownGT applies the GT predicate on the "last_down" field.
func LastDownGT(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldLastDown, v))
}

// LastDownGTE applies the GTE predicate on the "last_down" field.
func LastDownGTE(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldLastDown, v))
}

// LastDownLT applies the LT predicate on the "last_down" field.
func LastDownLT(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldLastDown, v))
}

// LastDownLTE applies the LTE predicate on the "last_down" field.
func LastDownLTE(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldLastDown, v))
}

// LastDownIsNil applies the IsNil predicate on the "last_down" field.
func LastDownIsNil() predicate.Server {
	return predicate.Server(sql.FieldIsNull(FieldLastDown))
}

// LastDownNotNil applies the NotNil predicate on the "last_down" field.
func LastDownNotNil() predicate.Server {
	return predicate.Server(sql.FieldNotNull(FieldLastDown))
}

// LastDowntimeDurationEQ applies the EQ predicate on the "last_downtime_duration" field.
func LastDowntimeDurationEQ(v int64) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldLastDowntimeDuration, v))
}

// LastDowntimeDurationNEQ applies the NEQ predicate on the "last_downtime_duration" field.
func LastDowntimeDurationNEQ(v int64) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldLastDowntimeDuration, v))
}

// LastDowntimeDurationIn applies the In predicate on the "last_downtime_duration" field.
func LastDowntimeDurationIn(vs ...int64) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldLastDowntimeDuration, vs...))
}

// LastDowntimeDurationNotIn applies the NotIn predicate on the "last_downtime_duration" field.
func LastDowntimeDurationNotIn(vs ...int64) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldLastDowntimeDuration, vs...))
}

// LastDowntimeDurationGT applies the GT predicate on the "last_downtime_duration" field.
func LastDowntimeDurationGT(v int64) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldLastDowntimeDuration, v))
}

// LastDowntimeDurationGTE applies the GTE predicate on the "last_downtime_duration" field.
func LastDowntimeDurationGTE(v int64) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldLastDowntimeDuration, v))
}

// LastDowntimeDurationLT applies the LT predicate on the "last_downtime_duration" field.
func LastDowntimeDurationLT(v int64) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldLastDowntimeDuration, v))
}

// LastDowntimeDurationLTE applies the LTE predicate on the "last_downtime_duration" field.
func LastDowntimeDurationLTE(v int64) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldLastDowntimeDuration, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Server {
	return predicate.Server(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Server {
	return predicate.Server(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Server {
	return predicate.Server(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasDowntimeIncidents applies the HasEdge predicate on the "downtime_incidents" edge.
func HasDowntimeIncidents() predicate.Server {
	return predicate.Server(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, DowntimeIncidentsTable, DowntimeIncidentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDowntimeIncidentsWith applies the HasEdge predicate on the "downtime_incidents" edge with a given conditions (other predicates).
func HasDowntimeIncidentsWith(preds ...predicate.DowntimeIncident) predicate.Server {
	return predicate.Server(func(s *sql.Selector) {
		step := newDowntimeIncidentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Server) predicate.Server {
	return predicate.Server(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Server) predicate.Server {
	return predicate.Server(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Server) predicate.Server {
	return predicate.Server(sql.NotPredicates(p))
}
