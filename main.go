package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	_ "modernc.org/sqlite" // Pure Go SQLite driver
)

var (
	db            DatabaseInterface
	smtpConfig    SMTPConfig
	checkInterval time.Duration
	httpClient    = &http.Client{
		Timeout: 10 * time.Second,
	}
)

func main() {
	// Load environment variables
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: .env file not found, using environment variables")
	}

	// Get database path from environment or use default
	dbPath := getEnv("DB_PATH", "./monitor.db")

	// Ensure directory exists
	if err := os.MkdirAll(filepath.Dir(dbPath), 0755); err != nil {
		log.Fatalf("Failed to create database directory: %v", err)
	}

	// Initialize database
	db, err = NewEntDatabase(dbPath)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize configuration
	initConfig()

	// Setup router
	r := gin.Default()
	setupRoutes(r)

	// Start monitoring in a separate goroutine
	go monitorServers()

	// Start the server
	port := getEnv("PORT", "8000")
	server := &http.Server{
		Addr:    ":" + port,
		Handler: r,
	}

	// Handle graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		log.Printf("Server running on port %s", port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal
	<-quit
	log.Println("Shutting down server...")

	// Give server time to finish current requests
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}
	log.Println("Server exiting")
}

func initConfig() {
	smtpConfig = SMTPConfig{
		Host:     getEnv("SMTP_HOST", "smtp.example.com"),
		Port:     getEnvAsInt("SMTP_PORT", 587),
		Username: getEnv("SMTP_USERNAME", ""),
		Password: getEnv("SMTP_PASSWORD", ""),
		From:     getEnv("SMTP_FROM", "<EMAIL>"),
		To:       getEnv("SMTP_TO", "<EMAIL>"),
	}

	// Default check interval is now 5 seconds instead of 60
	interval := getEnvAsInt("CHECK_INTERVAL", 5)
	checkInterval = time.Duration(interval) * time.Second
}

func setupRoutes(r *gin.Engine) {
	// Configure CORS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	r.Use(cors.New(config))

	// API Routes
	r.GET("/api/servers", getServers)
	r.GET("/api/servers/:id", getServer)
	r.POST("/api/servers", addServer)
	r.PUT("/api/servers/:id", updateServer)
	r.DELETE("/api/servers/:id", deleteServer)

	// Downtime Routes
	r.GET("/api/servers/:id/downtime", getServerDowntime)
	r.GET("/api/servers/:id/downtime/stats", getServerDowntimeStats)

	// Email Settings Routes
	r.GET("/api/settings/email", getEmailSettings)
	r.POST("/api/settings/email", saveEmailSettings)
	r.POST("/api/settings/email/test", testEmailSettings)

	// Serve static files
	r.Static("/js", "./static/js")
	r.Static("/css", "./static/css")

	// Serve the frontend
	r.GET("/", func(c *gin.Context) {
		c.File("./static/index.html")
	})

	// Serve the settings page
	r.GET("/settings.html", func(c *gin.Context) {
		c.File("./static/settings.html")
	})
}
