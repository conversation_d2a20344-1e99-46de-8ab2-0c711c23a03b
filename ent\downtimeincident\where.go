// Code generated by ent, DO NOT EDIT.

package downtimeincident

import (
	"server-monitor/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContainsFold(FieldID, id))
}

// ServerID applies equality check predicate on the "server_id" field. It's identical to ServerIDEQ.
func ServerID(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldServerID, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldEndTime, v))
}

// Duration applies equality check predicate on the "duration" field. It's identical to DurationEQ.
func Duration(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldDuration, v))
}

// StatusBefore applies equality check predicate on the "status_before" field. It's identical to StatusBeforeEQ.
func StatusBefore(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldStatusBefore, v))
}

// StatusAfter applies equality check predicate on the "status_after" field. It's identical to StatusAfterEQ.
func StatusAfter(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldStatusAfter, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldUpdatedAt, v))
}

// ServerIDEQ applies the EQ predicate on the "server_id" field.
func ServerIDEQ(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldServerID, v))
}

// ServerIDNEQ applies the NEQ predicate on the "server_id" field.
func ServerIDNEQ(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldServerID, v))
}

// ServerIDIn applies the In predicate on the "server_id" field.
func ServerIDIn(vs ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldServerID, vs...))
}

// ServerIDNotIn applies the NotIn predicate on the "server_id" field.
func ServerIDNotIn(vs ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldServerID, vs...))
}

// ServerIDGT applies the GT predicate on the "server_id" field.
func ServerIDGT(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldServerID, v))
}

// ServerIDGTE applies the GTE predicate on the "server_id" field.
func ServerIDGTE(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldServerID, v))
}

// ServerIDLT applies the LT predicate on the "server_id" field.
func ServerIDLT(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldServerID, v))
}

// ServerIDLTE applies the LTE predicate on the "server_id" field.
func ServerIDLTE(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldServerID, v))
}

// ServerIDContains applies the Contains predicate on the "server_id" field.
func ServerIDContains(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContains(FieldServerID, v))
}

// ServerIDHasPrefix applies the HasPrefix predicate on the "server_id" field.
func ServerIDHasPrefix(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldHasPrefix(FieldServerID, v))
}

// ServerIDHasSuffix applies the HasSuffix predicate on the "server_id" field.
func ServerIDHasSuffix(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldHasSuffix(FieldServerID, v))
}

// ServerIDEqualFold applies the EqualFold predicate on the "server_id" field.
func ServerIDEqualFold(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEqualFold(FieldServerID, v))
}

// ServerIDContainsFold applies the ContainsFold predicate on the "server_id" field.
func ServerIDContainsFold(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContainsFold(FieldServerID, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotNull(FieldEndTime))
}

// DurationEQ applies the EQ predicate on the "duration" field.
func DurationEQ(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldDuration, v))
}

// DurationNEQ applies the NEQ predicate on the "duration" field.
func DurationNEQ(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldDuration, v))
}

// DurationIn applies the In predicate on the "duration" field.
func DurationIn(vs ...int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldDuration, vs...))
}

// DurationNotIn applies the NotIn predicate on the "duration" field.
func DurationNotIn(vs ...int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldDuration, vs...))
}

// DurationGT applies the GT predicate on the "duration" field.
func DurationGT(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldDuration, v))
}

// DurationGTE applies the GTE predicate on the "duration" field.
func DurationGTE(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldDuration, v))
}

// DurationLT applies the LT predicate on the "duration" field.
func DurationLT(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldDuration, v))
}

// DurationLTE applies the LTE predicate on the "duration" field.
func DurationLTE(v int64) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldDuration, v))
}

// DurationIsNil applies the IsNil predicate on the "duration" field.
func DurationIsNil() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIsNull(FieldDuration))
}

// DurationNotNil applies the NotNil predicate on the "duration" field.
func DurationNotNil() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotNull(FieldDuration))
}

// StatusBeforeEQ applies the EQ predicate on the "status_before" field.
func StatusBeforeEQ(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldStatusBefore, v))
}

// StatusBeforeNEQ applies the NEQ predicate on the "status_before" field.
func StatusBeforeNEQ(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldStatusBefore, v))
}

// StatusBeforeIn applies the In predicate on the "status_before" field.
func StatusBeforeIn(vs ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldStatusBefore, vs...))
}

// StatusBeforeNotIn applies the NotIn predicate on the "status_before" field.
func StatusBeforeNotIn(vs ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldStatusBefore, vs...))
}

// StatusBeforeGT applies the GT predicate on the "status_before" field.
func StatusBeforeGT(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldStatusBefore, v))
}

// StatusBeforeGTE applies the GTE predicate on the "status_before" field.
func StatusBeforeGTE(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldStatusBefore, v))
}

// StatusBeforeLT applies the LT predicate on the "status_before" field.
func StatusBeforeLT(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldStatusBefore, v))
}

// StatusBeforeLTE applies the LTE predicate on the "status_before" field.
func StatusBeforeLTE(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldStatusBefore, v))
}

// StatusBeforeContains applies the Contains predicate on the "status_before" field.
func StatusBeforeContains(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContains(FieldStatusBefore, v))
}

// StatusBeforeHasPrefix applies the HasPrefix predicate on the "status_before" field.
func StatusBeforeHasPrefix(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldHasPrefix(FieldStatusBefore, v))
}

// StatusBeforeHasSuffix applies the HasSuffix predicate on the "status_before" field.
func StatusBeforeHasSuffix(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldHasSuffix(FieldStatusBefore, v))
}

// StatusBeforeEqualFold applies the EqualFold predicate on the "status_before" field.
func StatusBeforeEqualFold(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEqualFold(FieldStatusBefore, v))
}

// StatusBeforeContainsFold applies the ContainsFold predicate on the "status_before" field.
func StatusBeforeContainsFold(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContainsFold(FieldStatusBefore, v))
}

// StatusAfterEQ applies the EQ predicate on the "status_after" field.
func StatusAfterEQ(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldStatusAfter, v))
}

// StatusAfterNEQ applies the NEQ predicate on the "status_after" field.
func StatusAfterNEQ(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldStatusAfter, v))
}

// StatusAfterIn applies the In predicate on the "status_after" field.
func StatusAfterIn(vs ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldStatusAfter, vs...))
}

// StatusAfterNotIn applies the NotIn predicate on the "status_after" field.
func StatusAfterNotIn(vs ...string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldStatusAfter, vs...))
}

// StatusAfterGT applies the GT predicate on the "status_after" field.
func StatusAfterGT(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldStatusAfter, v))
}

// StatusAfterGTE applies the GTE predicate on the "status_after" field.
func StatusAfterGTE(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldStatusAfter, v))
}

// StatusAfterLT applies the LT predicate on the "status_after" field.
func StatusAfterLT(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldStatusAfter, v))
}

// StatusAfterLTE applies the LTE predicate on the "status_after" field.
func StatusAfterLTE(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldStatusAfter, v))
}

// StatusAfterContains applies the Contains predicate on the "status_after" field.
func StatusAfterContains(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContains(FieldStatusAfter, v))
}

// StatusAfterHasPrefix applies the HasPrefix predicate on the "status_after" field.
func StatusAfterHasPrefix(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldHasPrefix(FieldStatusAfter, v))
}

// StatusAfterHasSuffix applies the HasSuffix predicate on the "status_after" field.
func StatusAfterHasSuffix(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldHasSuffix(FieldStatusAfter, v))
}

// StatusAfterIsNil applies the IsNil predicate on the "status_after" field.
func StatusAfterIsNil() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIsNull(FieldStatusAfter))
}

// StatusAfterNotNil applies the NotNil predicate on the "status_after" field.
func StatusAfterNotNil() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotNull(FieldStatusAfter))
}

// StatusAfterEqualFold applies the EqualFold predicate on the "status_after" field.
func StatusAfterEqualFold(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEqualFold(FieldStatusAfter, v))
}

// StatusAfterContainsFold applies the ContainsFold predicate on the "status_after" field.
func StatusAfterContainsFold(v string) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldContainsFold(FieldStatusAfter, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasServer applies the HasEdge predicate on the "server" edge.
func HasServer() predicate.DowntimeIncident {
	return predicate.DowntimeIncident(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ServerTable, ServerColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasServerWith applies the HasEdge predicate on the "server" edge with a given conditions (other predicates).
func HasServerWith(preds ...predicate.Server) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(func(s *sql.Selector) {
		step := newServerStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DowntimeIncident) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DowntimeIncident) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DowntimeIncident) predicate.DowntimeIncident {
	return predicate.DowntimeIncident(sql.NotPredicates(p))
}
