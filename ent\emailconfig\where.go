// Code generated by ent, DO NOT EDIT.

package emailconfig

import (
	"server-monitor/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContainsFold(FieldID, id))
}

// Host applies equality check predicate on the "host" field. It's identical to HostEQ.
func Host(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldHost, v))
}

// Port applies equality check predicate on the "port" field. It's identical to PortEQ.
func Port(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldPort, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldUsername, v))
}

// Password applies equality check predicate on the "password" field. It's identical to PasswordEQ.
func Password(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldPassword, v))
}

// From applies equality check predicate on the "from" field. It's identical to FromEQ.
func From(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldFrom, v))
}

// To applies equality check predicate on the "to" field. It's identical to ToEQ.
func To(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldTo, v))
}

// Enabled applies equality check predicate on the "enabled" field. It's identical to EnabledEQ.
func Enabled(v bool) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldEnabled, v))
}

// HostEQ applies the EQ predicate on the "host" field.
func HostEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldHost, v))
}

// HostNEQ applies the NEQ predicate on the "host" field.
func HostNEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldHost, v))
}

// HostIn applies the In predicate on the "host" field.
func HostIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldHost, vs...))
}

// HostNotIn applies the NotIn predicate on the "host" field.
func HostNotIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldHost, vs...))
}

// HostGT applies the GT predicate on the "host" field.
func HostGT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldHost, v))
}

// HostGTE applies the GTE predicate on the "host" field.
func HostGTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldHost, v))
}

// HostLT applies the LT predicate on the "host" field.
func HostLT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldHost, v))
}

// HostLTE applies the LTE predicate on the "host" field.
func HostLTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldHost, v))
}

// HostContains applies the Contains predicate on the "host" field.
func HostContains(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContains(FieldHost, v))
}

// HostHasPrefix applies the HasPrefix predicate on the "host" field.
func HostHasPrefix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasPrefix(FieldHost, v))
}

// HostHasSuffix applies the HasSuffix predicate on the "host" field.
func HostHasSuffix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasSuffix(FieldHost, v))
}

// HostEqualFold applies the EqualFold predicate on the "host" field.
func HostEqualFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEqualFold(FieldHost, v))
}

// HostContainsFold applies the ContainsFold predicate on the "host" field.
func HostContainsFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContainsFold(FieldHost, v))
}

// PortEQ applies the EQ predicate on the "port" field.
func PortEQ(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldPort, v))
}

// PortNEQ applies the NEQ predicate on the "port" field.
func PortNEQ(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldPort, v))
}

// PortIn applies the In predicate on the "port" field.
func PortIn(vs ...int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldPort, vs...))
}

// PortNotIn applies the NotIn predicate on the "port" field.
func PortNotIn(vs ...int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldPort, vs...))
}

// PortGT applies the GT predicate on the "port" field.
func PortGT(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldPort, v))
}

// PortGTE applies the GTE predicate on the "port" field.
func PortGTE(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldPort, v))
}

// PortLT applies the LT predicate on the "port" field.
func PortLT(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldPort, v))
}

// PortLTE applies the LTE predicate on the "port" field.
func PortLTE(v int) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldPort, v))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameIsNil applies the IsNil predicate on the "username" field.
func UsernameIsNil() predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIsNull(FieldUsername))
}

// UsernameNotNil applies the NotNil predicate on the "username" field.
func UsernameNotNil() predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotNull(FieldUsername))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContainsFold(FieldUsername, v))
}

// PasswordEQ applies the EQ predicate on the "password" field.
func PasswordEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldPassword, v))
}

// PasswordNEQ applies the NEQ predicate on the "password" field.
func PasswordNEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldPassword, v))
}

// PasswordIn applies the In predicate on the "password" field.
func PasswordIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldPassword, vs...))
}

// PasswordNotIn applies the NotIn predicate on the "password" field.
func PasswordNotIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldPassword, vs...))
}

// PasswordGT applies the GT predicate on the "password" field.
func PasswordGT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldPassword, v))
}

// PasswordGTE applies the GTE predicate on the "password" field.
func PasswordGTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldPassword, v))
}

// PasswordLT applies the LT predicate on the "password" field.
func PasswordLT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldPassword, v))
}

// PasswordLTE applies the LTE predicate on the "password" field.
func PasswordLTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldPassword, v))
}

// PasswordContains applies the Contains predicate on the "password" field.
func PasswordContains(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContains(FieldPassword, v))
}

// PasswordHasPrefix applies the HasPrefix predicate on the "password" field.
func PasswordHasPrefix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasPrefix(FieldPassword, v))
}

// PasswordHasSuffix applies the HasSuffix predicate on the "password" field.
func PasswordHasSuffix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasSuffix(FieldPassword, v))
}

// PasswordIsNil applies the IsNil predicate on the "password" field.
func PasswordIsNil() predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIsNull(FieldPassword))
}

// PasswordNotNil applies the NotNil predicate on the "password" field.
func PasswordNotNil() predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotNull(FieldPassword))
}

// PasswordEqualFold applies the EqualFold predicate on the "password" field.
func PasswordEqualFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEqualFold(FieldPassword, v))
}

// PasswordContainsFold applies the ContainsFold predicate on the "password" field.
func PasswordContainsFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContainsFold(FieldPassword, v))
}

// FromEQ applies the EQ predicate on the "from" field.
func FromEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldFrom, v))
}

// FromNEQ applies the NEQ predicate on the "from" field.
func FromNEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldFrom, v))
}

// FromIn applies the In predicate on the "from" field.
func FromIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldFrom, vs...))
}

// FromNotIn applies the NotIn predicate on the "from" field.
func FromNotIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldFrom, vs...))
}

// FromGT applies the GT predicate on the "from" field.
func FromGT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldFrom, v))
}

// FromGTE applies the GTE predicate on the "from" field.
func FromGTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldFrom, v))
}

// FromLT applies the LT predicate on the "from" field.
func FromLT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldFrom, v))
}

// FromLTE applies the LTE predicate on the "from" field.
func FromLTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldFrom, v))
}

// FromContains applies the Contains predicate on the "from" field.
func FromContains(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContains(FieldFrom, v))
}

// FromHasPrefix applies the HasPrefix predicate on the "from" field.
func FromHasPrefix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasPrefix(FieldFrom, v))
}

// FromHasSuffix applies the HasSuffix predicate on the "from" field.
func FromHasSuffix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasSuffix(FieldFrom, v))
}

// FromEqualFold applies the EqualFold predicate on the "from" field.
func FromEqualFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEqualFold(FieldFrom, v))
}

// FromContainsFold applies the ContainsFold predicate on the "from" field.
func FromContainsFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContainsFold(FieldFrom, v))
}

// ToEQ applies the EQ predicate on the "to" field.
func ToEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldTo, v))
}

// ToNEQ applies the NEQ predicate on the "to" field.
func ToNEQ(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldTo, v))
}

// ToIn applies the In predicate on the "to" field.
func ToIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldIn(FieldTo, vs...))
}

// ToNotIn applies the NotIn predicate on the "to" field.
func ToNotIn(vs ...string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNotIn(FieldTo, vs...))
}

// ToGT applies the GT predicate on the "to" field.
func ToGT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGT(FieldTo, v))
}

// ToGTE applies the GTE predicate on the "to" field.
func ToGTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldGTE(FieldTo, v))
}

// ToLT applies the LT predicate on the "to" field.
func ToLT(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLT(FieldTo, v))
}

// ToLTE applies the LTE predicate on the "to" field.
func ToLTE(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldLTE(FieldTo, v))
}

// ToContains applies the Contains predicate on the "to" field.
func ToContains(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContains(FieldTo, v))
}

// ToHasPrefix applies the HasPrefix predicate on the "to" field.
func ToHasPrefix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasPrefix(FieldTo, v))
}

// ToHasSuffix applies the HasSuffix predicate on the "to" field.
func ToHasSuffix(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldHasSuffix(FieldTo, v))
}

// ToEqualFold applies the EqualFold predicate on the "to" field.
func ToEqualFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEqualFold(FieldTo, v))
}

// ToContainsFold applies the ContainsFold predicate on the "to" field.
func ToContainsFold(v string) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldContainsFold(FieldTo, v))
}

// EnabledEQ applies the EQ predicate on the "enabled" field.
func EnabledEQ(v bool) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldEQ(FieldEnabled, v))
}

// EnabledNEQ applies the NEQ predicate on the "enabled" field.
func EnabledNEQ(v bool) predicate.EmailConfig {
	return predicate.EmailConfig(sql.FieldNEQ(FieldEnabled, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.EmailConfig) predicate.EmailConfig {
	return predicate.EmailConfig(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.EmailConfig) predicate.EmailConfig {
	return predicate.EmailConfig(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.EmailConfig) predicate.EmailConfig {
	return predicate.EmailConfig(sql.NotPredicates(p))
}
