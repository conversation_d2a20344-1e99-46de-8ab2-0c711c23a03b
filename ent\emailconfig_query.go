// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/predicate"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// EmailConfigQuery is the builder for querying EmailConfig entities.
type EmailConfigQuery struct {
	config
	ctx        *QueryContext
	order      []emailconfig.OrderOption
	inters     []Interceptor
	predicates []predicate.EmailConfig
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the EmailConfigQuery builder.
func (ecq *EmailConfigQuery) Where(ps ...predicate.EmailConfig) *EmailConfigQuery {
	ecq.predicates = append(ecq.predicates, ps...)
	return ecq
}

// Limit the number of records to be returned by this query.
func (ecq *EmailConfigQuery) Limit(limit int) *EmailConfigQuery {
	ecq.ctx.Limit = &limit
	return ecq
}

// Offset to start from.
func (ecq *EmailConfigQuery) Offset(offset int) *EmailConfigQuery {
	ecq.ctx.Offset = &offset
	return ecq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ecq *EmailConfigQuery) Unique(unique bool) *EmailConfigQuery {
	ecq.ctx.Unique = &unique
	return ecq
}

// Order specifies how the records should be ordered.
func (ecq *EmailConfigQuery) Order(o ...emailconfig.OrderOption) *EmailConfigQuery {
	ecq.order = append(ecq.order, o...)
	return ecq
}

// First returns the first EmailConfig entity from the query.
// Returns a *NotFoundError when no EmailConfig was found.
func (ecq *EmailConfigQuery) First(ctx context.Context) (*EmailConfig, error) {
	nodes, err := ecq.Limit(1).All(setContextOp(ctx, ecq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{emailconfig.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ecq *EmailConfigQuery) FirstX(ctx context.Context) *EmailConfig {
	node, err := ecq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first EmailConfig ID from the query.
// Returns a *NotFoundError when no EmailConfig ID was found.
func (ecq *EmailConfigQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ecq.Limit(1).IDs(setContextOp(ctx, ecq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{emailconfig.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ecq *EmailConfigQuery) FirstIDX(ctx context.Context) string {
	id, err := ecq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single EmailConfig entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one EmailConfig entity is found.
// Returns a *NotFoundError when no EmailConfig entities are found.
func (ecq *EmailConfigQuery) Only(ctx context.Context) (*EmailConfig, error) {
	nodes, err := ecq.Limit(2).All(setContextOp(ctx, ecq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{emailconfig.Label}
	default:
		return nil, &NotSingularError{emailconfig.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ecq *EmailConfigQuery) OnlyX(ctx context.Context) *EmailConfig {
	node, err := ecq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only EmailConfig ID in the query.
// Returns a *NotSingularError when more than one EmailConfig ID is found.
// Returns a *NotFoundError when no entities are found.
func (ecq *EmailConfigQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ecq.Limit(2).IDs(setContextOp(ctx, ecq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{emailconfig.Label}
	default:
		err = &NotSingularError{emailconfig.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ecq *EmailConfigQuery) OnlyIDX(ctx context.Context) string {
	id, err := ecq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of EmailConfigs.
func (ecq *EmailConfigQuery) All(ctx context.Context) ([]*EmailConfig, error) {
	ctx = setContextOp(ctx, ecq.ctx, ent.OpQueryAll)
	if err := ecq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*EmailConfig, *EmailConfigQuery]()
	return withInterceptors[[]*EmailConfig](ctx, ecq, qr, ecq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ecq *EmailConfigQuery) AllX(ctx context.Context) []*EmailConfig {
	nodes, err := ecq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of EmailConfig IDs.
func (ecq *EmailConfigQuery) IDs(ctx context.Context) (ids []string, err error) {
	if ecq.ctx.Unique == nil && ecq.path != nil {
		ecq.Unique(true)
	}
	ctx = setContextOp(ctx, ecq.ctx, ent.OpQueryIDs)
	if err = ecq.Select(emailconfig.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ecq *EmailConfigQuery) IDsX(ctx context.Context) []string {
	ids, err := ecq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ecq *EmailConfigQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ecq.ctx, ent.OpQueryCount)
	if err := ecq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ecq, querierCount[*EmailConfigQuery](), ecq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ecq *EmailConfigQuery) CountX(ctx context.Context) int {
	count, err := ecq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ecq *EmailConfigQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ecq.ctx, ent.OpQueryExist)
	switch _, err := ecq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ecq *EmailConfigQuery) ExistX(ctx context.Context) bool {
	exist, err := ecq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the EmailConfigQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ecq *EmailConfigQuery) Clone() *EmailConfigQuery {
	if ecq == nil {
		return nil
	}
	return &EmailConfigQuery{
		config:     ecq.config,
		ctx:        ecq.ctx.Clone(),
		order:      append([]emailconfig.OrderOption{}, ecq.order...),
		inters:     append([]Interceptor{}, ecq.inters...),
		predicates: append([]predicate.EmailConfig{}, ecq.predicates...),
		// clone intermediate query.
		sql:  ecq.sql.Clone(),
		path: ecq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Host string `json:"host,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.EmailConfig.Query().
//		GroupBy(emailconfig.FieldHost).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ecq *EmailConfigQuery) GroupBy(field string, fields ...string) *EmailConfigGroupBy {
	ecq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &EmailConfigGroupBy{build: ecq}
	grbuild.flds = &ecq.ctx.Fields
	grbuild.label = emailconfig.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Host string `json:"host,omitempty"`
//	}
//
//	client.EmailConfig.Query().
//		Select(emailconfig.FieldHost).
//		Scan(ctx, &v)
func (ecq *EmailConfigQuery) Select(fields ...string) *EmailConfigSelect {
	ecq.ctx.Fields = append(ecq.ctx.Fields, fields...)
	sbuild := &EmailConfigSelect{EmailConfigQuery: ecq}
	sbuild.label = emailconfig.Label
	sbuild.flds, sbuild.scan = &ecq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a EmailConfigSelect configured with the given aggregations.
func (ecq *EmailConfigQuery) Aggregate(fns ...AggregateFunc) *EmailConfigSelect {
	return ecq.Select().Aggregate(fns...)
}

func (ecq *EmailConfigQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ecq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ecq); err != nil {
				return err
			}
		}
	}
	for _, f := range ecq.ctx.Fields {
		if !emailconfig.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ecq.path != nil {
		prev, err := ecq.path(ctx)
		if err != nil {
			return err
		}
		ecq.sql = prev
	}
	return nil
}

func (ecq *EmailConfigQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*EmailConfig, error) {
	var (
		nodes = []*EmailConfig{}
		_spec = ecq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*EmailConfig).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &EmailConfig{config: ecq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ecq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ecq *EmailConfigQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ecq.querySpec()
	_spec.Node.Columns = ecq.ctx.Fields
	if len(ecq.ctx.Fields) > 0 {
		_spec.Unique = ecq.ctx.Unique != nil && *ecq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ecq.driver, _spec)
}

func (ecq *EmailConfigQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(emailconfig.Table, emailconfig.Columns, sqlgraph.NewFieldSpec(emailconfig.FieldID, field.TypeString))
	_spec.From = ecq.sql
	if unique := ecq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ecq.path != nil {
		_spec.Unique = true
	}
	if fields := ecq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, emailconfig.FieldID)
		for i := range fields {
			if fields[i] != emailconfig.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ecq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ecq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ecq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ecq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ecq *EmailConfigQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ecq.driver.Dialect())
	t1 := builder.Table(emailconfig.Table)
	columns := ecq.ctx.Fields
	if len(columns) == 0 {
		columns = emailconfig.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ecq.sql != nil {
		selector = ecq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ecq.ctx.Unique != nil && *ecq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ecq.predicates {
		p(selector)
	}
	for _, p := range ecq.order {
		p(selector)
	}
	if offset := ecq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ecq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// EmailConfigGroupBy is the group-by builder for EmailConfig entities.
type EmailConfigGroupBy struct {
	selector
	build *EmailConfigQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ecgb *EmailConfigGroupBy) Aggregate(fns ...AggregateFunc) *EmailConfigGroupBy {
	ecgb.fns = append(ecgb.fns, fns...)
	return ecgb
}

// Scan applies the selector query and scans the result into the given value.
func (ecgb *EmailConfigGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ecgb.build.ctx, ent.OpQueryGroupBy)
	if err := ecgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*EmailConfigQuery, *EmailConfigGroupBy](ctx, ecgb.build, ecgb, ecgb.build.inters, v)
}

func (ecgb *EmailConfigGroupBy) sqlScan(ctx context.Context, root *EmailConfigQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ecgb.fns))
	for _, fn := range ecgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ecgb.flds)+len(ecgb.fns))
		for _, f := range *ecgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ecgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ecgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// EmailConfigSelect is the builder for selecting fields of EmailConfig entities.
type EmailConfigSelect struct {
	*EmailConfigQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ecs *EmailConfigSelect) Aggregate(fns ...AggregateFunc) *EmailConfigSelect {
	ecs.fns = append(ecs.fns, fns...)
	return ecs
}

// Scan applies the selector query and scans the result into the given value.
func (ecs *EmailConfigSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ecs.ctx, ent.OpQuerySelect)
	if err := ecs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*EmailConfigQuery, *EmailConfigSelect](ctx, ecs.EmailConfigQuery, ecs, ecs.inters, v)
}

func (ecs *EmailConfigSelect) sqlScan(ctx context.Context, root *EmailConfigQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ecs.fns))
	for _, fn := range ecs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ecs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ecs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
