# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache gcc musl-dev

# Download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o server-monitor .

# Final stage
FROM alpine:latest

WORKDIR /app

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Copy the binary from builder
COPY --from=builder /app/server-monitor .

# Copy static files from builder
COPY --from=builder /app/static ./static

# Create data directory and set permissions
RUN mkdir -p /data

# Set environment variables
ENV PORT=8000
ENV DB_PATH=/data/monitor.db

# Expose the application port
EXPOSE 8000

# Set the entrypoint
ENTRYPOINT ["./server-monitor"]
