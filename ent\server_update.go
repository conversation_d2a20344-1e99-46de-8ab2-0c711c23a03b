// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/predicate"
	"server-monitor/ent/server"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ServerUpdate is the builder for updating Server entities.
type ServerUpdate struct {
	config
	hooks    []Hook
	mutation *ServerMutation
}

// Where appends a list predicates to the ServerUpdate builder.
func (su *ServerUpdate) Where(ps ...predicate.Server) *ServerUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetName sets the "name" field.
func (su *ServerUpdate) SetName(s string) *ServerUpdate {
	su.mutation.SetName(s)
	return su
}

// SetNillableName sets the "name" field if the given value is not nil.
func (su *ServerUpdate) SetNillableName(s *string) *ServerUpdate {
	if s != nil {
		su.SetName(*s)
	}
	return su
}

// SetHost sets the "host" field.
func (su *ServerUpdate) SetHost(s string) *ServerUpdate {
	su.mutation.SetHost(s)
	return su
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (su *ServerUpdate) SetNillableHost(s *string) *ServerUpdate {
	if s != nil {
		su.SetHost(*s)
	}
	return su
}

// SetStatus sets the "status" field.
func (su *ServerUpdate) SetStatus(s string) *ServerUpdate {
	su.mutation.SetStatus(s)
	return su
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (su *ServerUpdate) SetNillableStatus(s *string) *ServerUpdate {
	if s != nil {
		su.SetStatus(*s)
	}
	return su
}

// SetLatency sets the "latency" field.
func (su *ServerUpdate) SetLatency(f float64) *ServerUpdate {
	su.mutation.ResetLatency()
	su.mutation.SetLatency(f)
	return su
}

// SetNillableLatency sets the "latency" field if the given value is not nil.
func (su *ServerUpdate) SetNillableLatency(f *float64) *ServerUpdate {
	if f != nil {
		su.SetLatency(*f)
	}
	return su
}

// AddLatency adds f to the "latency" field.
func (su *ServerUpdate) AddLatency(f float64) *ServerUpdate {
	su.mutation.AddLatency(f)
	return su
}

// SetCheckInterval sets the "check_interval" field.
func (su *ServerUpdate) SetCheckInterval(i int) *ServerUpdate {
	su.mutation.ResetCheckInterval()
	su.mutation.SetCheckInterval(i)
	return su
}

// SetNillableCheckInterval sets the "check_interval" field if the given value is not nil.
func (su *ServerUpdate) SetNillableCheckInterval(i *int) *ServerUpdate {
	if i != nil {
		su.SetCheckInterval(*i)
	}
	return su
}

// AddCheckInterval adds i to the "check_interval" field.
func (su *ServerUpdate) AddCheckInterval(i int) *ServerUpdate {
	su.mutation.AddCheckInterval(i)
	return su
}

// SetLastDown sets the "last_down" field.
func (su *ServerUpdate) SetLastDown(t time.Time) *ServerUpdate {
	su.mutation.SetLastDown(t)
	return su
}

// SetNillableLastDown sets the "last_down" field if the given value is not nil.
func (su *ServerUpdate) SetNillableLastDown(t *time.Time) *ServerUpdate {
	if t != nil {
		su.SetLastDown(*t)
	}
	return su
}

// ClearLastDown clears the value of the "last_down" field.
func (su *ServerUpdate) ClearLastDown() *ServerUpdate {
	su.mutation.ClearLastDown()
	return su
}

// SetLastDowntimeDuration sets the "last_downtime_duration" field.
func (su *ServerUpdate) SetLastDowntimeDuration(i int64) *ServerUpdate {
	su.mutation.ResetLastDowntimeDuration()
	su.mutation.SetLastDowntimeDuration(i)
	return su
}

// SetNillableLastDowntimeDuration sets the "last_downtime_duration" field if the given value is not nil.
func (su *ServerUpdate) SetNillableLastDowntimeDuration(i *int64) *ServerUpdate {
	if i != nil {
		su.SetLastDowntimeDuration(*i)
	}
	return su
}

// AddLastDowntimeDuration adds i to the "last_downtime_duration" field.
func (su *ServerUpdate) AddLastDowntimeDuration(i int64) *ServerUpdate {
	su.mutation.AddLastDowntimeDuration(i)
	return su
}

// SetUpdatedAt sets the "updated_at" field.
func (su *ServerUpdate) SetUpdatedAt(t time.Time) *ServerUpdate {
	su.mutation.SetUpdatedAt(t)
	return su
}

// AddDowntimeIncidentIDs adds the "downtime_incidents" edge to the DowntimeIncident entity by IDs.
func (su *ServerUpdate) AddDowntimeIncidentIDs(ids ...string) *ServerUpdate {
	su.mutation.AddDowntimeIncidentIDs(ids...)
	return su
}

// AddDowntimeIncidents adds the "downtime_incidents" edges to the DowntimeIncident entity.
func (su *ServerUpdate) AddDowntimeIncidents(d ...*DowntimeIncident) *ServerUpdate {
	ids := make([]string, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return su.AddDowntimeIncidentIDs(ids...)
}

// Mutation returns the ServerMutation object of the builder.
func (su *ServerUpdate) Mutation() *ServerMutation {
	return su.mutation
}

// ClearDowntimeIncidents clears all "downtime_incidents" edges to the DowntimeIncident entity.
func (su *ServerUpdate) ClearDowntimeIncidents() *ServerUpdate {
	su.mutation.ClearDowntimeIncidents()
	return su
}

// RemoveDowntimeIncidentIDs removes the "downtime_incidents" edge to DowntimeIncident entities by IDs.
func (su *ServerUpdate) RemoveDowntimeIncidentIDs(ids ...string) *ServerUpdate {
	su.mutation.RemoveDowntimeIncidentIDs(ids...)
	return su
}

// RemoveDowntimeIncidents removes "downtime_incidents" edges to DowntimeIncident entities.
func (su *ServerUpdate) RemoveDowntimeIncidents(d ...*DowntimeIncident) *ServerUpdate {
	ids := make([]string, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return su.RemoveDowntimeIncidentIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *ServerUpdate) Save(ctx context.Context) (int, error) {
	su.defaults()
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *ServerUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *ServerUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *ServerUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (su *ServerUpdate) defaults() {
	if _, ok := su.mutation.UpdatedAt(); !ok {
		v := server.UpdateDefaultUpdatedAt()
		su.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (su *ServerUpdate) check() error {
	if v, ok := su.mutation.Name(); ok {
		if err := server.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Server.name": %w`, err)}
		}
	}
	if v, ok := su.mutation.Host(); ok {
		if err := server.HostValidator(v); err != nil {
			return &ValidationError{Name: "host", err: fmt.Errorf(`ent: validator failed for field "Server.host": %w`, err)}
		}
	}
	if v, ok := su.mutation.CheckInterval(); ok {
		if err := server.CheckIntervalValidator(v); err != nil {
			return &ValidationError{Name: "check_interval", err: fmt.Errorf(`ent: validator failed for field "Server.check_interval": %w`, err)}
		}
	}
	return nil
}

func (su *ServerUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := su.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(server.Table, server.Columns, sqlgraph.NewFieldSpec(server.FieldID, field.TypeString))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.Name(); ok {
		_spec.SetField(server.FieldName, field.TypeString, value)
	}
	if value, ok := su.mutation.Host(); ok {
		_spec.SetField(server.FieldHost, field.TypeString, value)
	}
	if value, ok := su.mutation.Status(); ok {
		_spec.SetField(server.FieldStatus, field.TypeString, value)
	}
	if value, ok := su.mutation.Latency(); ok {
		_spec.SetField(server.FieldLatency, field.TypeFloat64, value)
	}
	if value, ok := su.mutation.AddedLatency(); ok {
		_spec.AddField(server.FieldLatency, field.TypeFloat64, value)
	}
	if value, ok := su.mutation.CheckInterval(); ok {
		_spec.SetField(server.FieldCheckInterval, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedCheckInterval(); ok {
		_spec.AddField(server.FieldCheckInterval, field.TypeInt, value)
	}
	if value, ok := su.mutation.LastDown(); ok {
		_spec.SetField(server.FieldLastDown, field.TypeTime, value)
	}
	if su.mutation.LastDownCleared() {
		_spec.ClearField(server.FieldLastDown, field.TypeTime)
	}
	if value, ok := su.mutation.LastDowntimeDuration(); ok {
		_spec.SetField(server.FieldLastDowntimeDuration, field.TypeInt64, value)
	}
	if value, ok := su.mutation.AddedLastDowntimeDuration(); ok {
		_spec.AddField(server.FieldLastDowntimeDuration, field.TypeInt64, value)
	}
	if value, ok := su.mutation.UpdatedAt(); ok {
		_spec.SetField(server.FieldUpdatedAt, field.TypeTime, value)
	}
	if su.mutation.DowntimeIncidentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.RemovedDowntimeIncidentsIDs(); len(nodes) > 0 && !su.mutation.DowntimeIncidentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.DowntimeIncidentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{server.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// ServerUpdateOne is the builder for updating a single Server entity.
type ServerUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ServerMutation
}

// SetName sets the "name" field.
func (suo *ServerUpdateOne) SetName(s string) *ServerUpdateOne {
	suo.mutation.SetName(s)
	return suo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableName(s *string) *ServerUpdateOne {
	if s != nil {
		suo.SetName(*s)
	}
	return suo
}

// SetHost sets the "host" field.
func (suo *ServerUpdateOne) SetHost(s string) *ServerUpdateOne {
	suo.mutation.SetHost(s)
	return suo
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableHost(s *string) *ServerUpdateOne {
	if s != nil {
		suo.SetHost(*s)
	}
	return suo
}

// SetStatus sets the "status" field.
func (suo *ServerUpdateOne) SetStatus(s string) *ServerUpdateOne {
	suo.mutation.SetStatus(s)
	return suo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableStatus(s *string) *ServerUpdateOne {
	if s != nil {
		suo.SetStatus(*s)
	}
	return suo
}

// SetLatency sets the "latency" field.
func (suo *ServerUpdateOne) SetLatency(f float64) *ServerUpdateOne {
	suo.mutation.ResetLatency()
	suo.mutation.SetLatency(f)
	return suo
}

// SetNillableLatency sets the "latency" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableLatency(f *float64) *ServerUpdateOne {
	if f != nil {
		suo.SetLatency(*f)
	}
	return suo
}

// AddLatency adds f to the "latency" field.
func (suo *ServerUpdateOne) AddLatency(f float64) *ServerUpdateOne {
	suo.mutation.AddLatency(f)
	return suo
}

// SetCheckInterval sets the "check_interval" field.
func (suo *ServerUpdateOne) SetCheckInterval(i int) *ServerUpdateOne {
	suo.mutation.ResetCheckInterval()
	suo.mutation.SetCheckInterval(i)
	return suo
}

// SetNillableCheckInterval sets the "check_interval" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableCheckInterval(i *int) *ServerUpdateOne {
	if i != nil {
		suo.SetCheckInterval(*i)
	}
	return suo
}

// AddCheckInterval adds i to the "check_interval" field.
func (suo *ServerUpdateOne) AddCheckInterval(i int) *ServerUpdateOne {
	suo.mutation.AddCheckInterval(i)
	return suo
}

// SetLastDown sets the "last_down" field.
func (suo *ServerUpdateOne) SetLastDown(t time.Time) *ServerUpdateOne {
	suo.mutation.SetLastDown(t)
	return suo
}

// SetNillableLastDown sets the "last_down" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableLastDown(t *time.Time) *ServerUpdateOne {
	if t != nil {
		suo.SetLastDown(*t)
	}
	return suo
}

// ClearLastDown clears the value of the "last_down" field.
func (suo *ServerUpdateOne) ClearLastDown() *ServerUpdateOne {
	suo.mutation.ClearLastDown()
	return suo
}

// SetLastDowntimeDuration sets the "last_downtime_duration" field.
func (suo *ServerUpdateOne) SetLastDowntimeDuration(i int64) *ServerUpdateOne {
	suo.mutation.ResetLastDowntimeDuration()
	suo.mutation.SetLastDowntimeDuration(i)
	return suo
}

// SetNillableLastDowntimeDuration sets the "last_downtime_duration" field if the given value is not nil.
func (suo *ServerUpdateOne) SetNillableLastDowntimeDuration(i *int64) *ServerUpdateOne {
	if i != nil {
		suo.SetLastDowntimeDuration(*i)
	}
	return suo
}

// AddLastDowntimeDuration adds i to the "last_downtime_duration" field.
func (suo *ServerUpdateOne) AddLastDowntimeDuration(i int64) *ServerUpdateOne {
	suo.mutation.AddLastDowntimeDuration(i)
	return suo
}

// SetUpdatedAt sets the "updated_at" field.
func (suo *ServerUpdateOne) SetUpdatedAt(t time.Time) *ServerUpdateOne {
	suo.mutation.SetUpdatedAt(t)
	return suo
}

// AddDowntimeIncidentIDs adds the "downtime_incidents" edge to the DowntimeIncident entity by IDs.
func (suo *ServerUpdateOne) AddDowntimeIncidentIDs(ids ...string) *ServerUpdateOne {
	suo.mutation.AddDowntimeIncidentIDs(ids...)
	return suo
}

// AddDowntimeIncidents adds the "downtime_incidents" edges to the DowntimeIncident entity.
func (suo *ServerUpdateOne) AddDowntimeIncidents(d ...*DowntimeIncident) *ServerUpdateOne {
	ids := make([]string, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return suo.AddDowntimeIncidentIDs(ids...)
}

// Mutation returns the ServerMutation object of the builder.
func (suo *ServerUpdateOne) Mutation() *ServerMutation {
	return suo.mutation
}

// ClearDowntimeIncidents clears all "downtime_incidents" edges to the DowntimeIncident entity.
func (suo *ServerUpdateOne) ClearDowntimeIncidents() *ServerUpdateOne {
	suo.mutation.ClearDowntimeIncidents()
	return suo
}

// RemoveDowntimeIncidentIDs removes the "downtime_incidents" edge to DowntimeIncident entities by IDs.
func (suo *ServerUpdateOne) RemoveDowntimeIncidentIDs(ids ...string) *ServerUpdateOne {
	suo.mutation.RemoveDowntimeIncidentIDs(ids...)
	return suo
}

// RemoveDowntimeIncidents removes "downtime_incidents" edges to DowntimeIncident entities.
func (suo *ServerUpdateOne) RemoveDowntimeIncidents(d ...*DowntimeIncident) *ServerUpdateOne {
	ids := make([]string, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return suo.RemoveDowntimeIncidentIDs(ids...)
}

// Where appends a list predicates to the ServerUpdate builder.
func (suo *ServerUpdateOne) Where(ps ...predicate.Server) *ServerUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *ServerUpdateOne) Select(field string, fields ...string) *ServerUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Server entity.
func (suo *ServerUpdateOne) Save(ctx context.Context) (*Server, error) {
	suo.defaults()
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *ServerUpdateOne) SaveX(ctx context.Context) *Server {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *ServerUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *ServerUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (suo *ServerUpdateOne) defaults() {
	if _, ok := suo.mutation.UpdatedAt(); !ok {
		v := server.UpdateDefaultUpdatedAt()
		suo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (suo *ServerUpdateOne) check() error {
	if v, ok := suo.mutation.Name(); ok {
		if err := server.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Server.name": %w`, err)}
		}
	}
	if v, ok := suo.mutation.Host(); ok {
		if err := server.HostValidator(v); err != nil {
			return &ValidationError{Name: "host", err: fmt.Errorf(`ent: validator failed for field "Server.host": %w`, err)}
		}
	}
	if v, ok := suo.mutation.CheckInterval(); ok {
		if err := server.CheckIntervalValidator(v); err != nil {
			return &ValidationError{Name: "check_interval", err: fmt.Errorf(`ent: validator failed for field "Server.check_interval": %w`, err)}
		}
	}
	return nil
}

func (suo *ServerUpdateOne) sqlSave(ctx context.Context) (_node *Server, err error) {
	if err := suo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(server.Table, server.Columns, sqlgraph.NewFieldSpec(server.FieldID, field.TypeString))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Server.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, server.FieldID)
		for _, f := range fields {
			if !server.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != server.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.Name(); ok {
		_spec.SetField(server.FieldName, field.TypeString, value)
	}
	if value, ok := suo.mutation.Host(); ok {
		_spec.SetField(server.FieldHost, field.TypeString, value)
	}
	if value, ok := suo.mutation.Status(); ok {
		_spec.SetField(server.FieldStatus, field.TypeString, value)
	}
	if value, ok := suo.mutation.Latency(); ok {
		_spec.SetField(server.FieldLatency, field.TypeFloat64, value)
	}
	if value, ok := suo.mutation.AddedLatency(); ok {
		_spec.AddField(server.FieldLatency, field.TypeFloat64, value)
	}
	if value, ok := suo.mutation.CheckInterval(); ok {
		_spec.SetField(server.FieldCheckInterval, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedCheckInterval(); ok {
		_spec.AddField(server.FieldCheckInterval, field.TypeInt, value)
	}
	if value, ok := suo.mutation.LastDown(); ok {
		_spec.SetField(server.FieldLastDown, field.TypeTime, value)
	}
	if suo.mutation.LastDownCleared() {
		_spec.ClearField(server.FieldLastDown, field.TypeTime)
	}
	if value, ok := suo.mutation.LastDowntimeDuration(); ok {
		_spec.SetField(server.FieldLastDowntimeDuration, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.AddedLastDowntimeDuration(); ok {
		_spec.AddField(server.FieldLastDowntimeDuration, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.UpdatedAt(); ok {
		_spec.SetField(server.FieldUpdatedAt, field.TypeTime, value)
	}
	if suo.mutation.DowntimeIncidentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.RemovedDowntimeIncidentsIDs(); len(nodes) > 0 && !suo.mutation.DowntimeIncidentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.DowntimeIncidentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Server{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{server.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}
