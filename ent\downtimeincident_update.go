// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/predicate"
	"server-monitor/ent/server"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DowntimeIncidentUpdate is the builder for updating DowntimeIncident entities.
type DowntimeIncidentUpdate struct {
	config
	hooks    []Hook
	mutation *DowntimeIncidentMutation
}

// Where appends a list predicates to the DowntimeIncidentUpdate builder.
func (diu *DowntimeIncidentUpdate) Where(ps ...predicate.DowntimeIncident) *DowntimeIncidentUpdate {
	diu.mutation.Where(ps...)
	return diu
}

// SetServerID sets the "server_id" field.
func (diu *DowntimeIncidentUpdate) SetServerID(s string) *DowntimeIncidentUpdate {
	diu.mutation.SetServerID(s)
	return diu
}

// SetNillableServerID sets the "server_id" field if the given value is not nil.
func (diu *DowntimeIncidentUpdate) SetNillableServerID(s *string) *DowntimeIncidentUpdate {
	if s != nil {
		diu.SetServerID(*s)
	}
	return diu
}

// SetStartTime sets the "start_time" field.
func (diu *DowntimeIncidentUpdate) SetStartTime(t time.Time) *DowntimeIncidentUpdate {
	diu.mutation.SetStartTime(t)
	return diu
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (diu *DowntimeIncidentUpdate) SetNillableStartTime(t *time.Time) *DowntimeIncidentUpdate {
	if t != nil {
		diu.SetStartTime(*t)
	}
	return diu
}

// SetEndTime sets the "end_time" field.
func (diu *DowntimeIncidentUpdate) SetEndTime(t time.Time) *DowntimeIncidentUpdate {
	diu.mutation.SetEndTime(t)
	return diu
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (diu *DowntimeIncidentUpdate) SetNillableEndTime(t *time.Time) *DowntimeIncidentUpdate {
	if t != nil {
		diu.SetEndTime(*t)
	}
	return diu
}

// ClearEndTime clears the value of the "end_time" field.
func (diu *DowntimeIncidentUpdate) ClearEndTime() *DowntimeIncidentUpdate {
	diu.mutation.ClearEndTime()
	return diu
}

// SetDuration sets the "duration" field.
func (diu *DowntimeIncidentUpdate) SetDuration(i int64) *DowntimeIncidentUpdate {
	diu.mutation.ResetDuration()
	diu.mutation.SetDuration(i)
	return diu
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (diu *DowntimeIncidentUpdate) SetNillableDuration(i *int64) *DowntimeIncidentUpdate {
	if i != nil {
		diu.SetDuration(*i)
	}
	return diu
}

// AddDuration adds i to the "duration" field.
func (diu *DowntimeIncidentUpdate) AddDuration(i int64) *DowntimeIncidentUpdate {
	diu.mutation.AddDuration(i)
	return diu
}

// ClearDuration clears the value of the "duration" field.
func (diu *DowntimeIncidentUpdate) ClearDuration() *DowntimeIncidentUpdate {
	diu.mutation.ClearDuration()
	return diu
}

// SetStatusBefore sets the "status_before" field.
func (diu *DowntimeIncidentUpdate) SetStatusBefore(s string) *DowntimeIncidentUpdate {
	diu.mutation.SetStatusBefore(s)
	return diu
}

// SetNillableStatusBefore sets the "status_before" field if the given value is not nil.
func (diu *DowntimeIncidentUpdate) SetNillableStatusBefore(s *string) *DowntimeIncidentUpdate {
	if s != nil {
		diu.SetStatusBefore(*s)
	}
	return diu
}

// SetStatusAfter sets the "status_after" field.
func (diu *DowntimeIncidentUpdate) SetStatusAfter(s string) *DowntimeIncidentUpdate {
	diu.mutation.SetStatusAfter(s)
	return diu
}

// SetNillableStatusAfter sets the "status_after" field if the given value is not nil.
func (diu *DowntimeIncidentUpdate) SetNillableStatusAfter(s *string) *DowntimeIncidentUpdate {
	if s != nil {
		diu.SetStatusAfter(*s)
	}
	return diu
}

// ClearStatusAfter clears the value of the "status_after" field.
func (diu *DowntimeIncidentUpdate) ClearStatusAfter() *DowntimeIncidentUpdate {
	diu.mutation.ClearStatusAfter()
	return diu
}

// SetUpdatedAt sets the "updated_at" field.
func (diu *DowntimeIncidentUpdate) SetUpdatedAt(t time.Time) *DowntimeIncidentUpdate {
	diu.mutation.SetUpdatedAt(t)
	return diu
}

// SetServer sets the "server" edge to the Server entity.
func (diu *DowntimeIncidentUpdate) SetServer(s *Server) *DowntimeIncidentUpdate {
	return diu.SetServerID(s.ID)
}

// Mutation returns the DowntimeIncidentMutation object of the builder.
func (diu *DowntimeIncidentUpdate) Mutation() *DowntimeIncidentMutation {
	return diu.mutation
}

// ClearServer clears the "server" edge to the Server entity.
func (diu *DowntimeIncidentUpdate) ClearServer() *DowntimeIncidentUpdate {
	diu.mutation.ClearServer()
	return diu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (diu *DowntimeIncidentUpdate) Save(ctx context.Context) (int, error) {
	diu.defaults()
	return withHooks(ctx, diu.sqlSave, diu.mutation, diu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (diu *DowntimeIncidentUpdate) SaveX(ctx context.Context) int {
	affected, err := diu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (diu *DowntimeIncidentUpdate) Exec(ctx context.Context) error {
	_, err := diu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (diu *DowntimeIncidentUpdate) ExecX(ctx context.Context) {
	if err := diu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (diu *DowntimeIncidentUpdate) defaults() {
	if _, ok := diu.mutation.UpdatedAt(); !ok {
		v := downtimeincident.UpdateDefaultUpdatedAt()
		diu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (diu *DowntimeIncidentUpdate) check() error {
	if v, ok := diu.mutation.ServerID(); ok {
		if err := downtimeincident.ServerIDValidator(v); err != nil {
			return &ValidationError{Name: "server_id", err: fmt.Errorf(`ent: validator failed for field "DowntimeIncident.server_id": %w`, err)}
		}
	}
	if v, ok := diu.mutation.StatusBefore(); ok {
		if err := downtimeincident.StatusBeforeValidator(v); err != nil {
			return &ValidationError{Name: "status_before", err: fmt.Errorf(`ent: validator failed for field "DowntimeIncident.status_before": %w`, err)}
		}
	}
	if diu.mutation.ServerCleared() && len(diu.mutation.ServerIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "DowntimeIncident.server"`)
	}
	return nil
}

func (diu *DowntimeIncidentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := diu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(downtimeincident.Table, downtimeincident.Columns, sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString))
	if ps := diu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := diu.mutation.StartTime(); ok {
		_spec.SetField(downtimeincident.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := diu.mutation.EndTime(); ok {
		_spec.SetField(downtimeincident.FieldEndTime, field.TypeTime, value)
	}
	if diu.mutation.EndTimeCleared() {
		_spec.ClearField(downtimeincident.FieldEndTime, field.TypeTime)
	}
	if value, ok := diu.mutation.Duration(); ok {
		_spec.SetField(downtimeincident.FieldDuration, field.TypeInt64, value)
	}
	if value, ok := diu.mutation.AddedDuration(); ok {
		_spec.AddField(downtimeincident.FieldDuration, field.TypeInt64, value)
	}
	if diu.mutation.DurationCleared() {
		_spec.ClearField(downtimeincident.FieldDuration, field.TypeInt64)
	}
	if value, ok := diu.mutation.StatusBefore(); ok {
		_spec.SetField(downtimeincident.FieldStatusBefore, field.TypeString, value)
	}
	if value, ok := diu.mutation.StatusAfter(); ok {
		_spec.SetField(downtimeincident.FieldStatusAfter, field.TypeString, value)
	}
	if diu.mutation.StatusAfterCleared() {
		_spec.ClearField(downtimeincident.FieldStatusAfter, field.TypeString)
	}
	if value, ok := diu.mutation.UpdatedAt(); ok {
		_spec.SetField(downtimeincident.FieldUpdatedAt, field.TypeTime, value)
	}
	if diu.mutation.ServerCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   downtimeincident.ServerTable,
			Columns: []string{downtimeincident.ServerColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(server.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := diu.mutation.ServerIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   downtimeincident.ServerTable,
			Columns: []string{downtimeincident.ServerColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(server.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, diu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{downtimeincident.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	diu.mutation.done = true
	return n, nil
}

// DowntimeIncidentUpdateOne is the builder for updating a single DowntimeIncident entity.
type DowntimeIncidentUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *DowntimeIncidentMutation
}

// SetServerID sets the "server_id" field.
func (diuo *DowntimeIncidentUpdateOne) SetServerID(s string) *DowntimeIncidentUpdateOne {
	diuo.mutation.SetServerID(s)
	return diuo
}

// SetNillableServerID sets the "server_id" field if the given value is not nil.
func (diuo *DowntimeIncidentUpdateOne) SetNillableServerID(s *string) *DowntimeIncidentUpdateOne {
	if s != nil {
		diuo.SetServerID(*s)
	}
	return diuo
}

// SetStartTime sets the "start_time" field.
func (diuo *DowntimeIncidentUpdateOne) SetStartTime(t time.Time) *DowntimeIncidentUpdateOne {
	diuo.mutation.SetStartTime(t)
	return diuo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (diuo *DowntimeIncidentUpdateOne) SetNillableStartTime(t *time.Time) *DowntimeIncidentUpdateOne {
	if t != nil {
		diuo.SetStartTime(*t)
	}
	return diuo
}

// SetEndTime sets the "end_time" field.
func (diuo *DowntimeIncidentUpdateOne) SetEndTime(t time.Time) *DowntimeIncidentUpdateOne {
	diuo.mutation.SetEndTime(t)
	return diuo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (diuo *DowntimeIncidentUpdateOne) SetNillableEndTime(t *time.Time) *DowntimeIncidentUpdateOne {
	if t != nil {
		diuo.SetEndTime(*t)
	}
	return diuo
}

// ClearEndTime clears the value of the "end_time" field.
func (diuo *DowntimeIncidentUpdateOne) ClearEndTime() *DowntimeIncidentUpdateOne {
	diuo.mutation.ClearEndTime()
	return diuo
}

// SetDuration sets the "duration" field.
func (diuo *DowntimeIncidentUpdateOne) SetDuration(i int64) *DowntimeIncidentUpdateOne {
	diuo.mutation.ResetDuration()
	diuo.mutation.SetDuration(i)
	return diuo
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (diuo *DowntimeIncidentUpdateOne) SetNillableDuration(i *int64) *DowntimeIncidentUpdateOne {
	if i != nil {
		diuo.SetDuration(*i)
	}
	return diuo
}

// AddDuration adds i to the "duration" field.
func (diuo *DowntimeIncidentUpdateOne) AddDuration(i int64) *DowntimeIncidentUpdateOne {
	diuo.mutation.AddDuration(i)
	return diuo
}

// ClearDuration clears the value of the "duration" field.
func (diuo *DowntimeIncidentUpdateOne) ClearDuration() *DowntimeIncidentUpdateOne {
	diuo.mutation.ClearDuration()
	return diuo
}

// SetStatusBefore sets the "status_before" field.
func (diuo *DowntimeIncidentUpdateOne) SetStatusBefore(s string) *DowntimeIncidentUpdateOne {
	diuo.mutation.SetStatusBefore(s)
	return diuo
}

// SetNillableStatusBefore sets the "status_before" field if the given value is not nil.
func (diuo *DowntimeIncidentUpdateOne) SetNillableStatusBefore(s *string) *DowntimeIncidentUpdateOne {
	if s != nil {
		diuo.SetStatusBefore(*s)
	}
	return diuo
}

// SetStatusAfter sets the "status_after" field.
func (diuo *DowntimeIncidentUpdateOne) SetStatusAfter(s string) *DowntimeIncidentUpdateOne {
	diuo.mutation.SetStatusAfter(s)
	return diuo
}

// SetNillableStatusAfter sets the "status_after" field if the given value is not nil.
func (diuo *DowntimeIncidentUpdateOne) SetNillableStatusAfter(s *string) *DowntimeIncidentUpdateOne {
	if s != nil {
		diuo.SetStatusAfter(*s)
	}
	return diuo
}

// ClearStatusAfter clears the value of the "status_after" field.
func (diuo *DowntimeIncidentUpdateOne) ClearStatusAfter() *DowntimeIncidentUpdateOne {
	diuo.mutation.ClearStatusAfter()
	return diuo
}

// SetUpdatedAt sets the "updated_at" field.
func (diuo *DowntimeIncidentUpdateOne) SetUpdatedAt(t time.Time) *DowntimeIncidentUpdateOne {
	diuo.mutation.SetUpdatedAt(t)
	return diuo
}

// SetServer sets the "server" edge to the Server entity.
func (diuo *DowntimeIncidentUpdateOne) SetServer(s *Server) *DowntimeIncidentUpdateOne {
	return diuo.SetServerID(s.ID)
}

// Mutation returns the DowntimeIncidentMutation object of the builder.
func (diuo *DowntimeIncidentUpdateOne) Mutation() *DowntimeIncidentMutation {
	return diuo.mutation
}

// ClearServer clears the "server" edge to the Server entity.
func (diuo *DowntimeIncidentUpdateOne) ClearServer() *DowntimeIncidentUpdateOne {
	diuo.mutation.ClearServer()
	return diuo
}

// Where appends a list predicates to the DowntimeIncidentUpdate builder.
func (diuo *DowntimeIncidentUpdateOne) Where(ps ...predicate.DowntimeIncident) *DowntimeIncidentUpdateOne {
	diuo.mutation.Where(ps...)
	return diuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (diuo *DowntimeIncidentUpdateOne) Select(field string, fields ...string) *DowntimeIncidentUpdateOne {
	diuo.fields = append([]string{field}, fields...)
	return diuo
}

// Save executes the query and returns the updated DowntimeIncident entity.
func (diuo *DowntimeIncidentUpdateOne) Save(ctx context.Context) (*DowntimeIncident, error) {
	diuo.defaults()
	return withHooks(ctx, diuo.sqlSave, diuo.mutation, diuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (diuo *DowntimeIncidentUpdateOne) SaveX(ctx context.Context) *DowntimeIncident {
	node, err := diuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (diuo *DowntimeIncidentUpdateOne) Exec(ctx context.Context) error {
	_, err := diuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (diuo *DowntimeIncidentUpdateOne) ExecX(ctx context.Context) {
	if err := diuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (diuo *DowntimeIncidentUpdateOne) defaults() {
	if _, ok := diuo.mutation.UpdatedAt(); !ok {
		v := downtimeincident.UpdateDefaultUpdatedAt()
		diuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (diuo *DowntimeIncidentUpdateOne) check() error {
	if v, ok := diuo.mutation.ServerID(); ok {
		if err := downtimeincident.ServerIDValidator(v); err != nil {
			return &ValidationError{Name: "server_id", err: fmt.Errorf(`ent: validator failed for field "DowntimeIncident.server_id": %w`, err)}
		}
	}
	if v, ok := diuo.mutation.StatusBefore(); ok {
		if err := downtimeincident.StatusBeforeValidator(v); err != nil {
			return &ValidationError{Name: "status_before", err: fmt.Errorf(`ent: validator failed for field "DowntimeIncident.status_before": %w`, err)}
		}
	}
	if diuo.mutation.ServerCleared() && len(diuo.mutation.ServerIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "DowntimeIncident.server"`)
	}
	return nil
}

func (diuo *DowntimeIncidentUpdateOne) sqlSave(ctx context.Context) (_node *DowntimeIncident, err error) {
	if err := diuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(downtimeincident.Table, downtimeincident.Columns, sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString))
	id, ok := diuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DowntimeIncident.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := diuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, downtimeincident.FieldID)
		for _, f := range fields {
			if !downtimeincident.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != downtimeincident.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := diuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := diuo.mutation.StartTime(); ok {
		_spec.SetField(downtimeincident.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := diuo.mutation.EndTime(); ok {
		_spec.SetField(downtimeincident.FieldEndTime, field.TypeTime, value)
	}
	if diuo.mutation.EndTimeCleared() {
		_spec.ClearField(downtimeincident.FieldEndTime, field.TypeTime)
	}
	if value, ok := diuo.mutation.Duration(); ok {
		_spec.SetField(downtimeincident.FieldDuration, field.TypeInt64, value)
	}
	if value, ok := diuo.mutation.AddedDuration(); ok {
		_spec.AddField(downtimeincident.FieldDuration, field.TypeInt64, value)
	}
	if diuo.mutation.DurationCleared() {
		_spec.ClearField(downtimeincident.FieldDuration, field.TypeInt64)
	}
	if value, ok := diuo.mutation.StatusBefore(); ok {
		_spec.SetField(downtimeincident.FieldStatusBefore, field.TypeString, value)
	}
	if value, ok := diuo.mutation.StatusAfter(); ok {
		_spec.SetField(downtimeincident.FieldStatusAfter, field.TypeString, value)
	}
	if diuo.mutation.StatusAfterCleared() {
		_spec.ClearField(downtimeincident.FieldStatusAfter, field.TypeString)
	}
	if value, ok := diuo.mutation.UpdatedAt(); ok {
		_spec.SetField(downtimeincident.FieldUpdatedAt, field.TypeTime, value)
	}
	if diuo.mutation.ServerCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   downtimeincident.ServerTable,
			Columns: []string{downtimeincident.ServerColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(server.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := diuo.mutation.ServerIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   downtimeincident.ServerTable,
			Columns: []string{downtimeincident.ServerColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(server.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &DowntimeIncident{config: diuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, diuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{downtimeincident.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	diuo.mutation.done = true
	return _node, nil
}
