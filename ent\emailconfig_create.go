// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/emailconfig"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// EmailConfigCreate is the builder for creating a EmailConfig entity.
type EmailConfigCreate struct {
	config
	mutation *EmailConfigMutation
	hooks    []Hook
}

// SetHost sets the "host" field.
func (ecc *EmailConfigCreate) SetHost(s string) *EmailConfigCreate {
	ecc.mutation.SetHost(s)
	return ecc
}

// SetPort sets the "port" field.
func (ecc *EmailConfigCreate) SetPort(i int) *EmailConfigCreate {
	ecc.mutation.SetPort(i)
	return ecc
}

// SetNillablePort sets the "port" field if the given value is not nil.
func (ecc *EmailConfigCreate) SetNillablePort(i *int) *EmailConfigCreate {
	if i != nil {
		ecc.SetPort(*i)
	}
	return ecc
}

// SetUsername sets the "username" field.
func (ecc *EmailConfigCreate) SetUsername(s string) *EmailConfigCreate {
	ecc.mutation.SetUsername(s)
	return ecc
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (ecc *EmailConfigCreate) SetNillableUsername(s *string) *EmailConfigCreate {
	if s != nil {
		ecc.SetUsername(*s)
	}
	return ecc
}

// SetPassword sets the "password" field.
func (ecc *EmailConfigCreate) SetPassword(s string) *EmailConfigCreate {
	ecc.mutation.SetPassword(s)
	return ecc
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (ecc *EmailConfigCreate) SetNillablePassword(s *string) *EmailConfigCreate {
	if s != nil {
		ecc.SetPassword(*s)
	}
	return ecc
}

// SetFrom sets the "from" field.
func (ecc *EmailConfigCreate) SetFrom(s string) *EmailConfigCreate {
	ecc.mutation.SetFrom(s)
	return ecc
}

// SetTo sets the "to" field.
func (ecc *EmailConfigCreate) SetTo(s string) *EmailConfigCreate {
	ecc.mutation.SetTo(s)
	return ecc
}

// SetEnabled sets the "enabled" field.
func (ecc *EmailConfigCreate) SetEnabled(b bool) *EmailConfigCreate {
	ecc.mutation.SetEnabled(b)
	return ecc
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (ecc *EmailConfigCreate) SetNillableEnabled(b *bool) *EmailConfigCreate {
	if b != nil {
		ecc.SetEnabled(*b)
	}
	return ecc
}

// SetID sets the "id" field.
func (ecc *EmailConfigCreate) SetID(s string) *EmailConfigCreate {
	ecc.mutation.SetID(s)
	return ecc
}

// Mutation returns the EmailConfigMutation object of the builder.
func (ecc *EmailConfigCreate) Mutation() *EmailConfigMutation {
	return ecc.mutation
}

// Save creates the EmailConfig in the database.
func (ecc *EmailConfigCreate) Save(ctx context.Context) (*EmailConfig, error) {
	ecc.defaults()
	return withHooks(ctx, ecc.sqlSave, ecc.mutation, ecc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ecc *EmailConfigCreate) SaveX(ctx context.Context) *EmailConfig {
	v, err := ecc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ecc *EmailConfigCreate) Exec(ctx context.Context) error {
	_, err := ecc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ecc *EmailConfigCreate) ExecX(ctx context.Context) {
	if err := ecc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ecc *EmailConfigCreate) defaults() {
	if _, ok := ecc.mutation.Port(); !ok {
		v := emailconfig.DefaultPort
		ecc.mutation.SetPort(v)
	}
	if _, ok := ecc.mutation.Enabled(); !ok {
		v := emailconfig.DefaultEnabled
		ecc.mutation.SetEnabled(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ecc *EmailConfigCreate) check() error {
	if _, ok := ecc.mutation.Host(); !ok {
		return &ValidationError{Name: "host", err: errors.New(`ent: missing required field "EmailConfig.host"`)}
	}
	if v, ok := ecc.mutation.Host(); ok {
		if err := emailconfig.HostValidator(v); err != nil {
			return &ValidationError{Name: "host", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.host": %w`, err)}
		}
	}
	if _, ok := ecc.mutation.Port(); !ok {
		return &ValidationError{Name: "port", err: errors.New(`ent: missing required field "EmailConfig.port"`)}
	}
	if _, ok := ecc.mutation.From(); !ok {
		return &ValidationError{Name: "from", err: errors.New(`ent: missing required field "EmailConfig.from"`)}
	}
	if v, ok := ecc.mutation.From(); ok {
		if err := emailconfig.FromValidator(v); err != nil {
			return &ValidationError{Name: "from", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.from": %w`, err)}
		}
	}
	if _, ok := ecc.mutation.To(); !ok {
		return &ValidationError{Name: "to", err: errors.New(`ent: missing required field "EmailConfig.to"`)}
	}
	if v, ok := ecc.mutation.To(); ok {
		if err := emailconfig.ToValidator(v); err != nil {
			return &ValidationError{Name: "to", err: fmt.Errorf(`ent: validator failed for field "EmailConfig.to": %w`, err)}
		}
	}
	if _, ok := ecc.mutation.Enabled(); !ok {
		return &ValidationError{Name: "enabled", err: errors.New(`ent: missing required field "EmailConfig.enabled"`)}
	}
	return nil
}

func (ecc *EmailConfigCreate) sqlSave(ctx context.Context) (*EmailConfig, error) {
	if err := ecc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ecc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ecc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected EmailConfig.ID type: %T", _spec.ID.Value)
		}
	}
	ecc.mutation.id = &_node.ID
	ecc.mutation.done = true
	return _node, nil
}

func (ecc *EmailConfigCreate) createSpec() (*EmailConfig, *sqlgraph.CreateSpec) {
	var (
		_node = &EmailConfig{config: ecc.config}
		_spec = sqlgraph.NewCreateSpec(emailconfig.Table, sqlgraph.NewFieldSpec(emailconfig.FieldID, field.TypeString))
	)
	if id, ok := ecc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ecc.mutation.Host(); ok {
		_spec.SetField(emailconfig.FieldHost, field.TypeString, value)
		_node.Host = value
	}
	if value, ok := ecc.mutation.Port(); ok {
		_spec.SetField(emailconfig.FieldPort, field.TypeInt, value)
		_node.Port = value
	}
	if value, ok := ecc.mutation.Username(); ok {
		_spec.SetField(emailconfig.FieldUsername, field.TypeString, value)
		_node.Username = value
	}
	if value, ok := ecc.mutation.Password(); ok {
		_spec.SetField(emailconfig.FieldPassword, field.TypeString, value)
		_node.Password = value
	}
	if value, ok := ecc.mutation.From(); ok {
		_spec.SetField(emailconfig.FieldFrom, field.TypeString, value)
		_node.From = value
	}
	if value, ok := ecc.mutation.To(); ok {
		_spec.SetField(emailconfig.FieldTo, field.TypeString, value)
		_node.To = value
	}
	if value, ok := ecc.mutation.Enabled(); ok {
		_spec.SetField(emailconfig.FieldEnabled, field.TypeBool, value)
		_node.Enabled = value
	}
	return _node, _spec
}

// EmailConfigCreateBulk is the builder for creating many EmailConfig entities in bulk.
type EmailConfigCreateBulk struct {
	config
	err      error
	builders []*EmailConfigCreate
}

// Save creates the EmailConfig entities in the database.
func (eccb *EmailConfigCreateBulk) Save(ctx context.Context) ([]*EmailConfig, error) {
	if eccb.err != nil {
		return nil, eccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(eccb.builders))
	nodes := make([]*EmailConfig, len(eccb.builders))
	mutators := make([]Mutator, len(eccb.builders))
	for i := range eccb.builders {
		func(i int, root context.Context) {
			builder := eccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*EmailConfigMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, eccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, eccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, eccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (eccb *EmailConfigCreateBulk) SaveX(ctx context.Context) []*EmailConfig {
	v, err := eccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (eccb *EmailConfigCreateBulk) Exec(ctx context.Context) error {
	_, err := eccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (eccb *EmailConfigCreateBulk) ExecX(ctx context.Context) {
	if err := eccb.Exec(ctx); err != nil {
		panic(err)
	}
}
