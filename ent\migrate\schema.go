// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// DowntimeIncidentsColumns holds the columns for the "downtime_incidents" table.
	DowntimeIncidentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "duration", Type: field.TypeInt64, Nullable: true},
		{Name: "status_before", Type: field.TypeString},
		{Name: "status_after", Type: field.TypeString, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "server_id", Type: field.TypeString},
	}
	// DowntimeIncidentsTable holds the schema information for the "downtime_incidents" table.
	DowntimeIncidentsTable = &schema.Table{
		Name:       "downtime_incidents",
		Columns:    DowntimeIncidentsColumns,
		PrimaryKey: []*schema.Column{DowntimeIncidentsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "downtime_incidents_servers_downtime_incidents",
				Columns:    []*schema.Column{DowntimeIncidentsColumns[8]},
				RefColumns: []*schema.Column{ServersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "downtimeincident_server_id",
				Unique:  false,
				Columns: []*schema.Column{DowntimeIncidentsColumns[8]},
			},
			{
				Name:    "downtimeincident_start_time",
				Unique:  false,
				Columns: []*schema.Column{DowntimeIncidentsColumns[1]},
			},
		},
	}
	// EmailConfigsColumns holds the columns for the "email_configs" table.
	EmailConfigsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "host", Type: field.TypeString},
		{Name: "port", Type: field.TypeInt, Default: 587},
		{Name: "username", Type: field.TypeString, Nullable: true},
		{Name: "password", Type: field.TypeString, Nullable: true},
		{Name: "from", Type: field.TypeString},
		{Name: "to", Type: field.TypeString},
		{Name: "enabled", Type: field.TypeBool, Default: false},
	}
	// EmailConfigsTable holds the schema information for the "email_configs" table.
	EmailConfigsTable = &schema.Table{
		Name:       "email_configs",
		Columns:    EmailConfigsColumns,
		PrimaryKey: []*schema.Column{EmailConfigsColumns[0]},
	}
	// ServersColumns holds the columns for the "servers" table.
	ServersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "name", Type: field.TypeString},
		{Name: "host", Type: field.TypeString},
		{Name: "status", Type: field.TypeString, Default: "unknown"},
		{Name: "latency", Type: field.TypeFloat64, Default: 0},
		{Name: "check_interval", Type: field.TypeInt, Default: 5},
		{Name: "last_down", Type: field.TypeTime, Nullable: true},
		{Name: "last_downtime_duration", Type: field.TypeInt64, Default: 0},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// ServersTable holds the schema information for the "servers" table.
	ServersTable = &schema.Table{
		Name:       "servers",
		Columns:    ServersColumns,
		PrimaryKey: []*schema.Column{ServersColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		DowntimeIncidentsTable,
		EmailConfigsTable,
		ServersTable,
	}
)

func init() {
	DowntimeIncidentsTable.ForeignKeys[0].RefTable = ServersTable
}
