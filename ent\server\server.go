// Code generated by ent, DO NOT EDIT.

package server

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the server type in the database.
	Label = "server"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldHost holds the string denoting the host field in the database.
	FieldHost = "host"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldLatency holds the string denoting the latency field in the database.
	FieldLatency = "latency"
	// FieldCheckInterval holds the string denoting the check_interval field in the database.
	FieldCheckInterval = "check_interval"
	// FieldLastDown holds the string denoting the last_down field in the database.
	FieldLastDown = "last_down"
	// FieldLastDowntimeDuration holds the string denoting the last_downtime_duration field in the database.
	FieldLastDowntimeDuration = "last_downtime_duration"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// EdgeDowntimeIncidents holds the string denoting the downtime_incidents edge name in mutations.
	EdgeDowntimeIncidents = "downtime_incidents"
	// Table holds the table name of the server in the database.
	Table = "servers"
	// DowntimeIncidentsTable is the table that holds the downtime_incidents relation/edge.
	DowntimeIncidentsTable = "downtime_incidents"
	// DowntimeIncidentsInverseTable is the table name for the DowntimeIncident entity.
	// It exists in this package in order to avoid circular dependency with the "downtimeincident" package.
	DowntimeIncidentsInverseTable = "downtime_incidents"
	// DowntimeIncidentsColumn is the table column denoting the downtime_incidents relation/edge.
	DowntimeIncidentsColumn = "server_id"
)

// Columns holds all SQL columns for server fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldHost,
	FieldStatus,
	FieldLatency,
	FieldCheckInterval,
	FieldLastDown,
	FieldLastDowntimeDuration,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// HostValidator is a validator for the "host" field. It is called by the builders before save.
	HostValidator func(string) error
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// DefaultLatency holds the default value on creation for the "latency" field.
	DefaultLatency float64
	// DefaultCheckInterval holds the default value on creation for the "check_interval" field.
	DefaultCheckInterval int
	// CheckIntervalValidator is a validator for the "check_interval" field. It is called by the builders before save.
	CheckIntervalValidator func(int) error
	// DefaultLastDowntimeDuration holds the default value on creation for the "last_downtime_duration" field.
	DefaultLastDowntimeDuration int64
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Server queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByHost orders the results by the host field.
func ByHost(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHost, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByLatency orders the results by the latency field.
func ByLatency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLatency, opts...).ToFunc()
}

// ByCheckInterval orders the results by the check_interval field.
func ByCheckInterval(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCheckInterval, opts...).ToFunc()
}

// ByLastDown orders the results by the last_down field.
func ByLastDown(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastDown, opts...).ToFunc()
}

// ByLastDowntimeDuration orders the results by the last_downtime_duration field.
func ByLastDowntimeDuration(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastDowntimeDuration, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDowntimeIncidentsCount orders the results by downtime_incidents count.
func ByDowntimeIncidentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDowntimeIncidentsStep(), opts...)
	}
}

// ByDowntimeIncidents orders the results by downtime_incidents terms.
func ByDowntimeIncidents(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDowntimeIncidentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newDowntimeIncidentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DowntimeIncidentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, DowntimeIncidentsTable, DowntimeIncidentsColumn),
	)
}
