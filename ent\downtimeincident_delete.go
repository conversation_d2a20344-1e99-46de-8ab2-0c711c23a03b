// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DowntimeIncidentDelete is the builder for deleting a DowntimeIncident entity.
type DowntimeIncidentDelete struct {
	config
	hooks    []Hook
	mutation *DowntimeIncidentMutation
}

// Where appends a list predicates to the DowntimeIncidentDelete builder.
func (did *DowntimeIncidentDelete) Where(ps ...predicate.DowntimeIncident) *DowntimeIncidentDelete {
	did.mutation.Where(ps...)
	return did
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (did *DowntimeIncidentDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, did.sqlExec, did.mutation, did.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (did *DowntimeIncidentDelete) ExecX(ctx context.Context) int {
	n, err := did.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (did *DowntimeIncidentDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(downtimeincident.Table, sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString))
	if ps := did.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, did.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	did.mutation.done = true
	return affected, err
}

// DowntimeIncidentDeleteOne is the builder for deleting a single DowntimeIncident entity.
type DowntimeIncidentDeleteOne struct {
	did *DowntimeIncidentDelete
}

// Where appends a list predicates to the DowntimeIncidentDelete builder.
func (dido *DowntimeIncidentDeleteOne) Where(ps ...predicate.DowntimeIncident) *DowntimeIncidentDeleteOne {
	dido.did.mutation.Where(ps...)
	return dido
}

// Exec executes the deletion query.
func (dido *DowntimeIncidentDeleteOne) Exec(ctx context.Context) error {
	n, err := dido.did.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{downtimeincident.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (dido *DowntimeIncidentDeleteOne) ExecX(ctx context.Context) {
	if err := dido.Exec(ctx); err != nil {
		panic(err)
	}
}
