// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/server"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// Server<PERSON><PERSON> is the builder for creating a Server entity.
type ServerCreate struct {
	config
	mutation *ServerMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (sc *ServerCreate) SetName(s string) *ServerCreate {
	sc.mutation.SetName(s)
	return sc
}

// SetHost sets the "host" field.
func (sc *ServerCreate) SetHost(s string) *ServerCreate {
	sc.mutation.SetHost(s)
	return sc
}

// SetStatus sets the "status" field.
func (sc *ServerCreate) SetStatus(s string) *ServerCreate {
	sc.mutation.SetStatus(s)
	return sc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (sc *ServerCreate) SetNillableStatus(s *string) *ServerCreate {
	if s != nil {
		sc.SetStatus(*s)
	}
	return sc
}

// SetLatency sets the "latency" field.
func (sc *ServerCreate) SetLatency(f float64) *ServerCreate {
	sc.mutation.SetLatency(f)
	return sc
}

// SetNillableLatency sets the "latency" field if the given value is not nil.
func (sc *ServerCreate) SetNillableLatency(f *float64) *ServerCreate {
	if f != nil {
		sc.SetLatency(*f)
	}
	return sc
}

// SetCheckInterval sets the "check_interval" field.
func (sc *ServerCreate) SetCheckInterval(i int) *ServerCreate {
	sc.mutation.SetCheckInterval(i)
	return sc
}

// SetNillableCheckInterval sets the "check_interval" field if the given value is not nil.
func (sc *ServerCreate) SetNillableCheckInterval(i *int) *ServerCreate {
	if i != nil {
		sc.SetCheckInterval(*i)
	}
	return sc
}

// SetLastDown sets the "last_down" field.
func (sc *ServerCreate) SetLastDown(t time.Time) *ServerCreate {
	sc.mutation.SetLastDown(t)
	return sc
}

// SetNillableLastDown sets the "last_down" field if the given value is not nil.
func (sc *ServerCreate) SetNillableLastDown(t *time.Time) *ServerCreate {
	if t != nil {
		sc.SetLastDown(*t)
	}
	return sc
}

// SetLastDowntimeDuration sets the "last_downtime_duration" field.
func (sc *ServerCreate) SetLastDowntimeDuration(i int64) *ServerCreate {
	sc.mutation.SetLastDowntimeDuration(i)
	return sc
}

// SetNillableLastDowntimeDuration sets the "last_downtime_duration" field if the given value is not nil.
func (sc *ServerCreate) SetNillableLastDowntimeDuration(i *int64) *ServerCreate {
	if i != nil {
		sc.SetLastDowntimeDuration(*i)
	}
	return sc
}

// SetCreatedAt sets the "created_at" field.
func (sc *ServerCreate) SetCreatedAt(t time.Time) *ServerCreate {
	sc.mutation.SetCreatedAt(t)
	return sc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sc *ServerCreate) SetNillableCreatedAt(t *time.Time) *ServerCreate {
	if t != nil {
		sc.SetCreatedAt(*t)
	}
	return sc
}

// SetUpdatedAt sets the "updated_at" field.
func (sc *ServerCreate) SetUpdatedAt(t time.Time) *ServerCreate {
	sc.mutation.SetUpdatedAt(t)
	return sc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sc *ServerCreate) SetNillableUpdatedAt(t *time.Time) *ServerCreate {
	if t != nil {
		sc.SetUpdatedAt(*t)
	}
	return sc
}

// SetID sets the "id" field.
func (sc *ServerCreate) SetID(s string) *ServerCreate {
	sc.mutation.SetID(s)
	return sc
}

// AddDowntimeIncidentIDs adds the "downtime_incidents" edge to the DowntimeIncident entity by IDs.
func (sc *ServerCreate) AddDowntimeIncidentIDs(ids ...string) *ServerCreate {
	sc.mutation.AddDowntimeIncidentIDs(ids...)
	return sc
}

// AddDowntimeIncidents adds the "downtime_incidents" edges to the DowntimeIncident entity.
func (sc *ServerCreate) AddDowntimeIncidents(d ...*DowntimeIncident) *ServerCreate {
	ids := make([]string, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return sc.AddDowntimeIncidentIDs(ids...)
}

// Mutation returns the ServerMutation object of the builder.
func (sc *ServerCreate) Mutation() *ServerMutation {
	return sc.mutation
}

// Save creates the Server in the database.
func (sc *ServerCreate) Save(ctx context.Context) (*Server, error) {
	sc.defaults()
	return withHooks(ctx, sc.sqlSave, sc.mutation, sc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sc *ServerCreate) SaveX(ctx context.Context) *Server {
	v, err := sc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sc *ServerCreate) Exec(ctx context.Context) error {
	_, err := sc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sc *ServerCreate) ExecX(ctx context.Context) {
	if err := sc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sc *ServerCreate) defaults() {
	if _, ok := sc.mutation.Status(); !ok {
		v := server.DefaultStatus
		sc.mutation.SetStatus(v)
	}
	if _, ok := sc.mutation.Latency(); !ok {
		v := server.DefaultLatency
		sc.mutation.SetLatency(v)
	}
	if _, ok := sc.mutation.CheckInterval(); !ok {
		v := server.DefaultCheckInterval
		sc.mutation.SetCheckInterval(v)
	}
	if _, ok := sc.mutation.LastDowntimeDuration(); !ok {
		v := server.DefaultLastDowntimeDuration
		sc.mutation.SetLastDowntimeDuration(v)
	}
	if _, ok := sc.mutation.CreatedAt(); !ok {
		v := server.DefaultCreatedAt()
		sc.mutation.SetCreatedAt(v)
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		v := server.DefaultUpdatedAt()
		sc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (sc *ServerCreate) check() error {
	if _, ok := sc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Server.name"`)}
	}
	if v, ok := sc.mutation.Name(); ok {
		if err := server.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Server.name": %w`, err)}
		}
	}
	if _, ok := sc.mutation.Host(); !ok {
		return &ValidationError{Name: "host", err: errors.New(`ent: missing required field "Server.host"`)}
	}
	if v, ok := sc.mutation.Host(); ok {
		if err := server.HostValidator(v); err != nil {
			return &ValidationError{Name: "host", err: fmt.Errorf(`ent: validator failed for field "Server.host": %w`, err)}
		}
	}
	if _, ok := sc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Server.status"`)}
	}
	if _, ok := sc.mutation.Latency(); !ok {
		return &ValidationError{Name: "latency", err: errors.New(`ent: missing required field "Server.latency"`)}
	}
	if _, ok := sc.mutation.CheckInterval(); !ok {
		return &ValidationError{Name: "check_interval", err: errors.New(`ent: missing required field "Server.check_interval"`)}
	}
	if v, ok := sc.mutation.CheckInterval(); ok {
		if err := server.CheckIntervalValidator(v); err != nil {
			return &ValidationError{Name: "check_interval", err: fmt.Errorf(`ent: validator failed for field "Server.check_interval": %w`, err)}
		}
	}
	if _, ok := sc.mutation.LastDowntimeDuration(); !ok {
		return &ValidationError{Name: "last_downtime_duration", err: errors.New(`ent: missing required field "Server.last_downtime_duration"`)}
	}
	if _, ok := sc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Server.created_at"`)}
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Server.updated_at"`)}
	}
	return nil
}

func (sc *ServerCreate) sqlSave(ctx context.Context) (*Server, error) {
	if err := sc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Server.ID type: %T", _spec.ID.Value)
		}
	}
	sc.mutation.id = &_node.ID
	sc.mutation.done = true
	return _node, nil
}

func (sc *ServerCreate) createSpec() (*Server, *sqlgraph.CreateSpec) {
	var (
		_node = &Server{config: sc.config}
		_spec = sqlgraph.NewCreateSpec(server.Table, sqlgraph.NewFieldSpec(server.FieldID, field.TypeString))
	)
	if id, ok := sc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := sc.mutation.Name(); ok {
		_spec.SetField(server.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := sc.mutation.Host(); ok {
		_spec.SetField(server.FieldHost, field.TypeString, value)
		_node.Host = value
	}
	if value, ok := sc.mutation.Status(); ok {
		_spec.SetField(server.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := sc.mutation.Latency(); ok {
		_spec.SetField(server.FieldLatency, field.TypeFloat64, value)
		_node.Latency = value
	}
	if value, ok := sc.mutation.CheckInterval(); ok {
		_spec.SetField(server.FieldCheckInterval, field.TypeInt, value)
		_node.CheckInterval = value
	}
	if value, ok := sc.mutation.LastDown(); ok {
		_spec.SetField(server.FieldLastDown, field.TypeTime, value)
		_node.LastDown = &value
	}
	if value, ok := sc.mutation.LastDowntimeDuration(); ok {
		_spec.SetField(server.FieldLastDowntimeDuration, field.TypeInt64, value)
		_node.LastDowntimeDuration = value
	}
	if value, ok := sc.mutation.CreatedAt(); ok {
		_spec.SetField(server.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sc.mutation.UpdatedAt(); ok {
		_spec.SetField(server.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if nodes := sc.mutation.DowntimeIncidentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   server.DowntimeIncidentsTable,
			Columns: []string{server.DowntimeIncidentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// ServerCreateBulk is the builder for creating many Server entities in bulk.
type ServerCreateBulk struct {
	config
	err      error
	builders []*ServerCreate
}

// Save creates the Server entities in the database.
func (scb *ServerCreateBulk) Save(ctx context.Context) ([]*Server, error) {
	if scb.err != nil {
		return nil, scb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(scb.builders))
	nodes := make([]*Server, len(scb.builders))
	mutators := make([]Mutator, len(scb.builders))
	for i := range scb.builders {
		func(i int, root context.Context) {
			builder := scb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ServerMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, scb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, scb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, scb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (scb *ServerCreateBulk) SaveX(ctx context.Context) []*Server {
	v, err := scb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scb *ServerCreateBulk) Exec(ctx context.Context) error {
	_, err := scb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scb *ServerCreateBulk) ExecX(ctx context.Context) {
	if err := scb.Exec(ctx); err != nil {
		panic(err)
	}
}
