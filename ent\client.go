// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"server-monitor/ent/migrate"

	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/server"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// DowntimeIncident is the client for interacting with the DowntimeIncident builders.
	DowntimeIncident *DowntimeIncidentClient
	// EmailConfig is the client for interacting with the EmailConfig builders.
	EmailConfig *EmailConfigClient
	// Server is the client for interacting with the Server builders.
	Server *ServerClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.DowntimeIncident = NewDowntimeIncidentClient(c.config)
	c.EmailConfig = NewEmailConfigClient(c.config)
	c.Server = NewServerClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:              ctx,
		config:           cfg,
		DowntimeIncident: NewDowntimeIncidentClient(cfg),
		EmailConfig:      NewEmailConfigClient(cfg),
		Server:           NewServerClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:              ctx,
		config:           cfg,
		DowntimeIncident: NewDowntimeIncidentClient(cfg),
		EmailConfig:      NewEmailConfigClient(cfg),
		Server:           NewServerClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		DowntimeIncident.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.DowntimeIncident.Use(hooks...)
	c.EmailConfig.Use(hooks...)
	c.Server.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.DowntimeIncident.Intercept(interceptors...)
	c.EmailConfig.Intercept(interceptors...)
	c.Server.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *DowntimeIncidentMutation:
		return c.DowntimeIncident.mutate(ctx, m)
	case *EmailConfigMutation:
		return c.EmailConfig.mutate(ctx, m)
	case *ServerMutation:
		return c.Server.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// DowntimeIncidentClient is a client for the DowntimeIncident schema.
type DowntimeIncidentClient struct {
	config
}

// NewDowntimeIncidentClient returns a client for the DowntimeIncident from the given config.
func NewDowntimeIncidentClient(c config) *DowntimeIncidentClient {
	return &DowntimeIncidentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `downtimeincident.Hooks(f(g(h())))`.
func (c *DowntimeIncidentClient) Use(hooks ...Hook) {
	c.hooks.DowntimeIncident = append(c.hooks.DowntimeIncident, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `downtimeincident.Intercept(f(g(h())))`.
func (c *DowntimeIncidentClient) Intercept(interceptors ...Interceptor) {
	c.inters.DowntimeIncident = append(c.inters.DowntimeIncident, interceptors...)
}

// Create returns a builder for creating a DowntimeIncident entity.
func (c *DowntimeIncidentClient) Create() *DowntimeIncidentCreate {
	mutation := newDowntimeIncidentMutation(c.config, OpCreate)
	return &DowntimeIncidentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DowntimeIncident entities.
func (c *DowntimeIncidentClient) CreateBulk(builders ...*DowntimeIncidentCreate) *DowntimeIncidentCreateBulk {
	return &DowntimeIncidentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DowntimeIncidentClient) MapCreateBulk(slice any, setFunc func(*DowntimeIncidentCreate, int)) *DowntimeIncidentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DowntimeIncidentCreateBulk{err: fmt.Errorf("calling to DowntimeIncidentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DowntimeIncidentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DowntimeIncidentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DowntimeIncident.
func (c *DowntimeIncidentClient) Update() *DowntimeIncidentUpdate {
	mutation := newDowntimeIncidentMutation(c.config, OpUpdate)
	return &DowntimeIncidentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DowntimeIncidentClient) UpdateOne(di *DowntimeIncident) *DowntimeIncidentUpdateOne {
	mutation := newDowntimeIncidentMutation(c.config, OpUpdateOne, withDowntimeIncident(di))
	return &DowntimeIncidentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DowntimeIncidentClient) UpdateOneID(id string) *DowntimeIncidentUpdateOne {
	mutation := newDowntimeIncidentMutation(c.config, OpUpdateOne, withDowntimeIncidentID(id))
	return &DowntimeIncidentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DowntimeIncident.
func (c *DowntimeIncidentClient) Delete() *DowntimeIncidentDelete {
	mutation := newDowntimeIncidentMutation(c.config, OpDelete)
	return &DowntimeIncidentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DowntimeIncidentClient) DeleteOne(di *DowntimeIncident) *DowntimeIncidentDeleteOne {
	return c.DeleteOneID(di.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DowntimeIncidentClient) DeleteOneID(id string) *DowntimeIncidentDeleteOne {
	builder := c.Delete().Where(downtimeincident.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DowntimeIncidentDeleteOne{builder}
}

// Query returns a query builder for DowntimeIncident.
func (c *DowntimeIncidentClient) Query() *DowntimeIncidentQuery {
	return &DowntimeIncidentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDowntimeIncident},
		inters: c.Interceptors(),
	}
}

// Get returns a DowntimeIncident entity by its id.
func (c *DowntimeIncidentClient) Get(ctx context.Context, id string) (*DowntimeIncident, error) {
	return c.Query().Where(downtimeincident.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DowntimeIncidentClient) GetX(ctx context.Context, id string) *DowntimeIncident {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryServer queries the server edge of a DowntimeIncident.
func (c *DowntimeIncidentClient) QueryServer(di *DowntimeIncident) *ServerQuery {
	query := (&ServerClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := di.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(downtimeincident.Table, downtimeincident.FieldID, id),
			sqlgraph.To(server.Table, server.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, downtimeincident.ServerTable, downtimeincident.ServerColumn),
		)
		fromV = sqlgraph.Neighbors(di.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DowntimeIncidentClient) Hooks() []Hook {
	return c.hooks.DowntimeIncident
}

// Interceptors returns the client interceptors.
func (c *DowntimeIncidentClient) Interceptors() []Interceptor {
	return c.inters.DowntimeIncident
}

func (c *DowntimeIncidentClient) mutate(ctx context.Context, m *DowntimeIncidentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DowntimeIncidentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DowntimeIncidentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DowntimeIncidentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DowntimeIncidentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DowntimeIncident mutation op: %q", m.Op())
	}
}

// EmailConfigClient is a client for the EmailConfig schema.
type EmailConfigClient struct {
	config
}

// NewEmailConfigClient returns a client for the EmailConfig from the given config.
func NewEmailConfigClient(c config) *EmailConfigClient {
	return &EmailConfigClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `emailconfig.Hooks(f(g(h())))`.
func (c *EmailConfigClient) Use(hooks ...Hook) {
	c.hooks.EmailConfig = append(c.hooks.EmailConfig, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `emailconfig.Intercept(f(g(h())))`.
func (c *EmailConfigClient) Intercept(interceptors ...Interceptor) {
	c.inters.EmailConfig = append(c.inters.EmailConfig, interceptors...)
}

// Create returns a builder for creating a EmailConfig entity.
func (c *EmailConfigClient) Create() *EmailConfigCreate {
	mutation := newEmailConfigMutation(c.config, OpCreate)
	return &EmailConfigCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of EmailConfig entities.
func (c *EmailConfigClient) CreateBulk(builders ...*EmailConfigCreate) *EmailConfigCreateBulk {
	return &EmailConfigCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EmailConfigClient) MapCreateBulk(slice any, setFunc func(*EmailConfigCreate, int)) *EmailConfigCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EmailConfigCreateBulk{err: fmt.Errorf("calling to EmailConfigClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EmailConfigCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EmailConfigCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for EmailConfig.
func (c *EmailConfigClient) Update() *EmailConfigUpdate {
	mutation := newEmailConfigMutation(c.config, OpUpdate)
	return &EmailConfigUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EmailConfigClient) UpdateOne(ec *EmailConfig) *EmailConfigUpdateOne {
	mutation := newEmailConfigMutation(c.config, OpUpdateOne, withEmailConfig(ec))
	return &EmailConfigUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EmailConfigClient) UpdateOneID(id string) *EmailConfigUpdateOne {
	mutation := newEmailConfigMutation(c.config, OpUpdateOne, withEmailConfigID(id))
	return &EmailConfigUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for EmailConfig.
func (c *EmailConfigClient) Delete() *EmailConfigDelete {
	mutation := newEmailConfigMutation(c.config, OpDelete)
	return &EmailConfigDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EmailConfigClient) DeleteOne(ec *EmailConfig) *EmailConfigDeleteOne {
	return c.DeleteOneID(ec.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EmailConfigClient) DeleteOneID(id string) *EmailConfigDeleteOne {
	builder := c.Delete().Where(emailconfig.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EmailConfigDeleteOne{builder}
}

// Query returns a query builder for EmailConfig.
func (c *EmailConfigClient) Query() *EmailConfigQuery {
	return &EmailConfigQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEmailConfig},
		inters: c.Interceptors(),
	}
}

// Get returns a EmailConfig entity by its id.
func (c *EmailConfigClient) Get(ctx context.Context, id string) (*EmailConfig, error) {
	return c.Query().Where(emailconfig.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EmailConfigClient) GetX(ctx context.Context, id string) *EmailConfig {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *EmailConfigClient) Hooks() []Hook {
	return c.hooks.EmailConfig
}

// Interceptors returns the client interceptors.
func (c *EmailConfigClient) Interceptors() []Interceptor {
	return c.inters.EmailConfig
}

func (c *EmailConfigClient) mutate(ctx context.Context, m *EmailConfigMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EmailConfigCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EmailConfigUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EmailConfigUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EmailConfigDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown EmailConfig mutation op: %q", m.Op())
	}
}

// ServerClient is a client for the Server schema.
type ServerClient struct {
	config
}

// NewServerClient returns a client for the Server from the given config.
func NewServerClient(c config) *ServerClient {
	return &ServerClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `server.Hooks(f(g(h())))`.
func (c *ServerClient) Use(hooks ...Hook) {
	c.hooks.Server = append(c.hooks.Server, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `server.Intercept(f(g(h())))`.
func (c *ServerClient) Intercept(interceptors ...Interceptor) {
	c.inters.Server = append(c.inters.Server, interceptors...)
}

// Create returns a builder for creating a Server entity.
func (c *ServerClient) Create() *ServerCreate {
	mutation := newServerMutation(c.config, OpCreate)
	return &ServerCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Server entities.
func (c *ServerClient) CreateBulk(builders ...*ServerCreate) *ServerCreateBulk {
	return &ServerCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ServerClient) MapCreateBulk(slice any, setFunc func(*ServerCreate, int)) *ServerCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ServerCreateBulk{err: fmt.Errorf("calling to ServerClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ServerCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ServerCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Server.
func (c *ServerClient) Update() *ServerUpdate {
	mutation := newServerMutation(c.config, OpUpdate)
	return &ServerUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ServerClient) UpdateOne(s *Server) *ServerUpdateOne {
	mutation := newServerMutation(c.config, OpUpdateOne, withServer(s))
	return &ServerUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ServerClient) UpdateOneID(id string) *ServerUpdateOne {
	mutation := newServerMutation(c.config, OpUpdateOne, withServerID(id))
	return &ServerUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Server.
func (c *ServerClient) Delete() *ServerDelete {
	mutation := newServerMutation(c.config, OpDelete)
	return &ServerDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ServerClient) DeleteOne(s *Server) *ServerDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ServerClient) DeleteOneID(id string) *ServerDeleteOne {
	builder := c.Delete().Where(server.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ServerDeleteOne{builder}
}

// Query returns a query builder for Server.
func (c *ServerClient) Query() *ServerQuery {
	return &ServerQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeServer},
		inters: c.Interceptors(),
	}
}

// Get returns a Server entity by its id.
func (c *ServerClient) Get(ctx context.Context, id string) (*Server, error) {
	return c.Query().Where(server.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ServerClient) GetX(ctx context.Context, id string) *Server {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDowntimeIncidents queries the downtime_incidents edge of a Server.
func (c *ServerClient) QueryDowntimeIncidents(s *Server) *DowntimeIncidentQuery {
	query := (&DowntimeIncidentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(server.Table, server.FieldID, id),
			sqlgraph.To(downtimeincident.Table, downtimeincident.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, server.DowntimeIncidentsTable, server.DowntimeIncidentsColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ServerClient) Hooks() []Hook {
	return c.hooks.Server
}

// Interceptors returns the client interceptors.
func (c *ServerClient) Interceptors() []Interceptor {
	return c.inters.Server
}

func (c *ServerClient) mutate(ctx context.Context, m *ServerMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ServerCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ServerUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ServerUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ServerDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Server mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		DowntimeIncident, EmailConfig, Server []ent.Hook
	}
	inters struct {
		DowntimeIncident, EmailConfig, Server []ent.Interceptor
	}
)
