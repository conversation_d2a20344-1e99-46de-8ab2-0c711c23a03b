// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"server-monitor/ent/emailconfig"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// EmailConfig is the model entity for the EmailConfig schema.
type EmailConfig struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// Host holds the value of the "host" field.
	Host string `json:"host,omitempty"`
	// Port holds the value of the "port" field.
	Port int `json:"port,omitempty"`
	// Username holds the value of the "username" field.
	Username string `json:"username,omitempty"`
	// Password holds the value of the "password" field.
	Password string `json:"password,omitempty"`
	// From holds the value of the "from" field.
	From string `json:"from,omitempty"`
	// To holds the value of the "to" field.
	To string `json:"to,omitempty"`
	// Enabled holds the value of the "enabled" field.
	Enabled      bool `json:"enabled,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*EmailConfig) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case emailconfig.FieldEnabled:
			values[i] = new(sql.NullBool)
		case emailconfig.FieldPort:
			values[i] = new(sql.NullInt64)
		case emailconfig.FieldID, emailconfig.FieldHost, emailconfig.FieldUsername, emailconfig.FieldPassword, emailconfig.FieldFrom, emailconfig.FieldTo:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the EmailConfig fields.
func (ec *EmailConfig) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case emailconfig.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				ec.ID = value.String
			}
		case emailconfig.FieldHost:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field host", values[i])
			} else if value.Valid {
				ec.Host = value.String
			}
		case emailconfig.FieldPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field port", values[i])
			} else if value.Valid {
				ec.Port = int(value.Int64)
			}
		case emailconfig.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				ec.Username = value.String
			}
		case emailconfig.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				ec.Password = value.String
			}
		case emailconfig.FieldFrom:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field from", values[i])
			} else if value.Valid {
				ec.From = value.String
			}
		case emailconfig.FieldTo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field to", values[i])
			} else if value.Valid {
				ec.To = value.String
			}
		case emailconfig.FieldEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enabled", values[i])
			} else if value.Valid {
				ec.Enabled = value.Bool
			}
		default:
			ec.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the EmailConfig.
// This includes values selected through modifiers, order, etc.
func (ec *EmailConfig) Value(name string) (ent.Value, error) {
	return ec.selectValues.Get(name)
}

// Update returns a builder for updating this EmailConfig.
// Note that you need to call EmailConfig.Unwrap() before calling this method if this EmailConfig
// was returned from a transaction, and the transaction was committed or rolled back.
func (ec *EmailConfig) Update() *EmailConfigUpdateOne {
	return NewEmailConfigClient(ec.config).UpdateOne(ec)
}

// Unwrap unwraps the EmailConfig entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ec *EmailConfig) Unwrap() *EmailConfig {
	_tx, ok := ec.config.driver.(*txDriver)
	if !ok {
		panic("ent: EmailConfig is not a transactional entity")
	}
	ec.config.driver = _tx.drv
	return ec
}

// String implements the fmt.Stringer.
func (ec *EmailConfig) String() string {
	var builder strings.Builder
	builder.WriteString("EmailConfig(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ec.ID))
	builder.WriteString("host=")
	builder.WriteString(ec.Host)
	builder.WriteString(", ")
	builder.WriteString("port=")
	builder.WriteString(fmt.Sprintf("%v", ec.Port))
	builder.WriteString(", ")
	builder.WriteString("username=")
	builder.WriteString(ec.Username)
	builder.WriteString(", ")
	builder.WriteString("password=")
	builder.WriteString(ec.Password)
	builder.WriteString(", ")
	builder.WriteString("from=")
	builder.WriteString(ec.From)
	builder.WriteString(", ")
	builder.WriteString("to=")
	builder.WriteString(ec.To)
	builder.WriteString(", ")
	builder.WriteString("enabled=")
	builder.WriteString(fmt.Sprintf("%v", ec.Enabled))
	builder.WriteByte(')')
	return builder.String()
}

// EmailConfigs is a parsable slice of EmailConfig.
type EmailConfigs []*EmailConfig
