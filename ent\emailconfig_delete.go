// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"server-monitor/ent/emailconfig"
	"server-monitor/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// EmailConfigDelete is the builder for deleting a EmailConfig entity.
type EmailConfigDelete struct {
	config
	hooks    []Hook
	mutation *EmailConfigMutation
}

// Where appends a list predicates to the EmailConfigDelete builder.
func (ecd *EmailConfigDelete) Where(ps ...predicate.EmailConfig) *EmailConfigDelete {
	ecd.mutation.Where(ps...)
	return ecd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ecd *EmailConfigDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ecd.sqlExec, ecd.mutation, ecd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ecd *EmailConfigDelete) ExecX(ctx context.Context) int {
	n, err := ecd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ecd *EmailConfigDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(emailconfig.Table, sqlgraph.NewFieldSpec(emailconfig.FieldID, field.TypeString))
	if ps := ecd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ecd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ecd.mutation.done = true
	return affected, err
}

// EmailConfigDeleteOne is the builder for deleting a single EmailConfig entity.
type EmailConfigDeleteOne struct {
	ecd *EmailConfigDelete
}

// Where appends a list predicates to the EmailConfigDelete builder.
func (ecdo *EmailConfigDeleteOne) Where(ps ...predicate.EmailConfig) *EmailConfigDeleteOne {
	ecdo.ecd.mutation.Where(ps...)
	return ecdo
}

// Exec executes the deletion query.
func (ecdo *EmailConfigDeleteOne) Exec(ctx context.Context) error {
	n, err := ecdo.ecd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{emailconfig.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ecdo *EmailConfigDeleteOne) ExecX(ctx context.Context) {
	if err := ecdo.Exec(ctx); err != nil {
		panic(err)
	}
}
