// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/server"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// DowntimeIncident is the model entity for the DowntimeIncident schema.
type DowntimeIncident struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// ServerID holds the value of the "server_id" field.
	ServerID string `json:"server_id,omitempty"`
	// StartTime holds the value of the "start_time" field.
	StartTime time.Time `json:"start_time,omitempty"`
	// EndTime holds the value of the "end_time" field.
	EndTime *time.Time `json:"end_time,omitempty"`
	// Duration holds the value of the "duration" field.
	Duration *int64 `json:"duration,omitempty"`
	// StatusBefore holds the value of the "status_before" field.
	StatusBefore string `json:"status_before,omitempty"`
	// StatusAfter holds the value of the "status_after" field.
	StatusAfter *string `json:"status_after,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DowntimeIncidentQuery when eager-loading is set.
	Edges        DowntimeIncidentEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DowntimeIncidentEdges holds the relations/edges for other nodes in the graph.
type DowntimeIncidentEdges struct {
	// Server holds the value of the server edge.
	Server *Server `json:"server,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// ServerOrErr returns the Server value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DowntimeIncidentEdges) ServerOrErr() (*Server, error) {
	if e.Server != nil {
		return e.Server, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: server.Label}
	}
	return nil, &NotLoadedError{edge: "server"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DowntimeIncident) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case downtimeincident.FieldDuration:
			values[i] = new(sql.NullInt64)
		case downtimeincident.FieldID, downtimeincident.FieldServerID, downtimeincident.FieldStatusBefore, downtimeincident.FieldStatusAfter:
			values[i] = new(sql.NullString)
		case downtimeincident.FieldStartTime, downtimeincident.FieldEndTime, downtimeincident.FieldCreatedAt, downtimeincident.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DowntimeIncident fields.
func (di *DowntimeIncident) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case downtimeincident.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				di.ID = value.String
			}
		case downtimeincident.FieldServerID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field server_id", values[i])
			} else if value.Valid {
				di.ServerID = value.String
			}
		case downtimeincident.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				di.StartTime = value.Time
			}
		case downtimeincident.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				di.EndTime = new(time.Time)
				*di.EndTime = value.Time
			}
		case downtimeincident.FieldDuration:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field duration", values[i])
			} else if value.Valid {
				di.Duration = new(int64)
				*di.Duration = value.Int64
			}
		case downtimeincident.FieldStatusBefore:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status_before", values[i])
			} else if value.Valid {
				di.StatusBefore = value.String
			}
		case downtimeincident.FieldStatusAfter:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status_after", values[i])
			} else if value.Valid {
				di.StatusAfter = new(string)
				*di.StatusAfter = value.String
			}
		case downtimeincident.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				di.CreatedAt = value.Time
			}
		case downtimeincident.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				di.UpdatedAt = value.Time
			}
		default:
			di.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DowntimeIncident.
// This includes values selected through modifiers, order, etc.
func (di *DowntimeIncident) Value(name string) (ent.Value, error) {
	return di.selectValues.Get(name)
}

// QueryServer queries the "server" edge of the DowntimeIncident entity.
func (di *DowntimeIncident) QueryServer() *ServerQuery {
	return NewDowntimeIncidentClient(di.config).QueryServer(di)
}

// Update returns a builder for updating this DowntimeIncident.
// Note that you need to call DowntimeIncident.Unwrap() before calling this method if this DowntimeIncident
// was returned from a transaction, and the transaction was committed or rolled back.
func (di *DowntimeIncident) Update() *DowntimeIncidentUpdateOne {
	return NewDowntimeIncidentClient(di.config).UpdateOne(di)
}

// Unwrap unwraps the DowntimeIncident entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (di *DowntimeIncident) Unwrap() *DowntimeIncident {
	_tx, ok := di.config.driver.(*txDriver)
	if !ok {
		panic("ent: DowntimeIncident is not a transactional entity")
	}
	di.config.driver = _tx.drv
	return di
}

// String implements the fmt.Stringer.
func (di *DowntimeIncident) String() string {
	var builder strings.Builder
	builder.WriteString("DowntimeIncident(")
	builder.WriteString(fmt.Sprintf("id=%v, ", di.ID))
	builder.WriteString("server_id=")
	builder.WriteString(di.ServerID)
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(di.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := di.EndTime; v != nil {
		builder.WriteString("end_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := di.Duration; v != nil {
		builder.WriteString("duration=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("status_before=")
	builder.WriteString(di.StatusBefore)
	builder.WriteString(", ")
	if v := di.StatusAfter; v != nil {
		builder.WriteString("status_after=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(di.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(di.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// DowntimeIncidents is a parsable slice of DowntimeIncident.
type DowntimeIncidents []*DowntimeIncident
