// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"server-monitor/ent/downtimeincident"
	"server-monitor/ent/server"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DowntimeIncidentCreate is the builder for creating a DowntimeIncident entity.
type DowntimeIncidentCreate struct {
	config
	mutation *DowntimeIncidentMutation
	hooks    []Hook
}

// SetServerID sets the "server_id" field.
func (dic *DowntimeIncidentCreate) SetServerID(s string) *DowntimeIncidentCreate {
	dic.mutation.SetServerID(s)
	return dic
}

// SetStartTime sets the "start_time" field.
func (dic *DowntimeIncidentCreate) SetStartTime(t time.Time) *DowntimeIncidentCreate {
	dic.mutation.SetStartTime(t)
	return dic
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (dic *DowntimeIncidentCreate) SetNillableStartTime(t *time.Time) *DowntimeIncidentCreate {
	if t != nil {
		dic.SetStartTime(*t)
	}
	return dic
}

// SetEndTime sets the "end_time" field.
func (dic *DowntimeIncidentCreate) SetEndTime(t time.Time) *DowntimeIncidentCreate {
	dic.mutation.SetEndTime(t)
	return dic
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (dic *DowntimeIncidentCreate) SetNillableEndTime(t *time.Time) *DowntimeIncidentCreate {
	if t != nil {
		dic.SetEndTime(*t)
	}
	return dic
}

// SetDuration sets the "duration" field.
func (dic *DowntimeIncidentCreate) SetDuration(i int64) *DowntimeIncidentCreate {
	dic.mutation.SetDuration(i)
	return dic
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (dic *DowntimeIncidentCreate) SetNillableDuration(i *int64) *DowntimeIncidentCreate {
	if i != nil {
		dic.SetDuration(*i)
	}
	return dic
}

// SetStatusBefore sets the "status_before" field.
func (dic *DowntimeIncidentCreate) SetStatusBefore(s string) *DowntimeIncidentCreate {
	dic.mutation.SetStatusBefore(s)
	return dic
}

// SetStatusAfter sets the "status_after" field.
func (dic *DowntimeIncidentCreate) SetStatusAfter(s string) *DowntimeIncidentCreate {
	dic.mutation.SetStatusAfter(s)
	return dic
}

// SetNillableStatusAfter sets the "status_after" field if the given value is not nil.
func (dic *DowntimeIncidentCreate) SetNillableStatusAfter(s *string) *DowntimeIncidentCreate {
	if s != nil {
		dic.SetStatusAfter(*s)
	}
	return dic
}

// SetCreatedAt sets the "created_at" field.
func (dic *DowntimeIncidentCreate) SetCreatedAt(t time.Time) *DowntimeIncidentCreate {
	dic.mutation.SetCreatedAt(t)
	return dic
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (dic *DowntimeIncidentCreate) SetNillableCreatedAt(t *time.Time) *DowntimeIncidentCreate {
	if t != nil {
		dic.SetCreatedAt(*t)
	}
	return dic
}

// SetUpdatedAt sets the "updated_at" field.
func (dic *DowntimeIncidentCreate) SetUpdatedAt(t time.Time) *DowntimeIncidentCreate {
	dic.mutation.SetUpdatedAt(t)
	return dic
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (dic *DowntimeIncidentCreate) SetNillableUpdatedAt(t *time.Time) *DowntimeIncidentCreate {
	if t != nil {
		dic.SetUpdatedAt(*t)
	}
	return dic
}

// SetID sets the "id" field.
func (dic *DowntimeIncidentCreate) SetID(s string) *DowntimeIncidentCreate {
	dic.mutation.SetID(s)
	return dic
}

// SetServer sets the "server" edge to the Server entity.
func (dic *DowntimeIncidentCreate) SetServer(s *Server) *DowntimeIncidentCreate {
	return dic.SetServerID(s.ID)
}

// Mutation returns the DowntimeIncidentMutation object of the builder.
func (dic *DowntimeIncidentCreate) Mutation() *DowntimeIncidentMutation {
	return dic.mutation
}

// Save creates the DowntimeIncident in the database.
func (dic *DowntimeIncidentCreate) Save(ctx context.Context) (*DowntimeIncident, error) {
	dic.defaults()
	return withHooks(ctx, dic.sqlSave, dic.mutation, dic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dic *DowntimeIncidentCreate) SaveX(ctx context.Context) *DowntimeIncident {
	v, err := dic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dic *DowntimeIncidentCreate) Exec(ctx context.Context) error {
	_, err := dic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dic *DowntimeIncidentCreate) ExecX(ctx context.Context) {
	if err := dic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dic *DowntimeIncidentCreate) defaults() {
	if _, ok := dic.mutation.StartTime(); !ok {
		v := downtimeincident.DefaultStartTime()
		dic.mutation.SetStartTime(v)
	}
	if _, ok := dic.mutation.CreatedAt(); !ok {
		v := downtimeincident.DefaultCreatedAt()
		dic.mutation.SetCreatedAt(v)
	}
	if _, ok := dic.mutation.UpdatedAt(); !ok {
		v := downtimeincident.DefaultUpdatedAt()
		dic.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dic *DowntimeIncidentCreate) check() error {
	if _, ok := dic.mutation.ServerID(); !ok {
		return &ValidationError{Name: "server_id", err: errors.New(`ent: missing required field "DowntimeIncident.server_id"`)}
	}
	if v, ok := dic.mutation.ServerID(); ok {
		if err := downtimeincident.ServerIDValidator(v); err != nil {
			return &ValidationError{Name: "server_id", err: fmt.Errorf(`ent: validator failed for field "DowntimeIncident.server_id": %w`, err)}
		}
	}
	if _, ok := dic.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "DowntimeIncident.start_time"`)}
	}
	if _, ok := dic.mutation.StatusBefore(); !ok {
		return &ValidationError{Name: "status_before", err: errors.New(`ent: missing required field "DowntimeIncident.status_before"`)}
	}
	if v, ok := dic.mutation.StatusBefore(); ok {
		if err := downtimeincident.StatusBeforeValidator(v); err != nil {
			return &ValidationError{Name: "status_before", err: fmt.Errorf(`ent: validator failed for field "DowntimeIncident.status_before": %w`, err)}
		}
	}
	if _, ok := dic.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "DowntimeIncident.created_at"`)}
	}
	if _, ok := dic.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "DowntimeIncident.updated_at"`)}
	}
	if len(dic.mutation.ServerIDs()) == 0 {
		return &ValidationError{Name: "server", err: errors.New(`ent: missing required edge "DowntimeIncident.server"`)}
	}
	return nil
}

func (dic *DowntimeIncidentCreate) sqlSave(ctx context.Context) (*DowntimeIncident, error) {
	if err := dic.check(); err != nil {
		return nil, err
	}
	_node, _spec := dic.createSpec()
	if err := sqlgraph.CreateNode(ctx, dic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected DowntimeIncident.ID type: %T", _spec.ID.Value)
		}
	}
	dic.mutation.id = &_node.ID
	dic.mutation.done = true
	return _node, nil
}

func (dic *DowntimeIncidentCreate) createSpec() (*DowntimeIncident, *sqlgraph.CreateSpec) {
	var (
		_node = &DowntimeIncident{config: dic.config}
		_spec = sqlgraph.NewCreateSpec(downtimeincident.Table, sqlgraph.NewFieldSpec(downtimeincident.FieldID, field.TypeString))
	)
	if id, ok := dic.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := dic.mutation.StartTime(); ok {
		_spec.SetField(downtimeincident.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := dic.mutation.EndTime(); ok {
		_spec.SetField(downtimeincident.FieldEndTime, field.TypeTime, value)
		_node.EndTime = &value
	}
	if value, ok := dic.mutation.Duration(); ok {
		_spec.SetField(downtimeincident.FieldDuration, field.TypeInt64, value)
		_node.Duration = &value
	}
	if value, ok := dic.mutation.StatusBefore(); ok {
		_spec.SetField(downtimeincident.FieldStatusBefore, field.TypeString, value)
		_node.StatusBefore = value
	}
	if value, ok := dic.mutation.StatusAfter(); ok {
		_spec.SetField(downtimeincident.FieldStatusAfter, field.TypeString, value)
		_node.StatusAfter = &value
	}
	if value, ok := dic.mutation.CreatedAt(); ok {
		_spec.SetField(downtimeincident.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := dic.mutation.UpdatedAt(); ok {
		_spec.SetField(downtimeincident.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if nodes := dic.mutation.ServerIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   downtimeincident.ServerTable,
			Columns: []string{downtimeincident.ServerColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(server.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ServerID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// DowntimeIncidentCreateBulk is the builder for creating many DowntimeIncident entities in bulk.
type DowntimeIncidentCreateBulk struct {
	config
	err      error
	builders []*DowntimeIncidentCreate
}

// Save creates the DowntimeIncident entities in the database.
func (dicb *DowntimeIncidentCreateBulk) Save(ctx context.Context) ([]*DowntimeIncident, error) {
	if dicb.err != nil {
		return nil, dicb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dicb.builders))
	nodes := make([]*DowntimeIncident, len(dicb.builders))
	mutators := make([]Mutator, len(dicb.builders))
	for i := range dicb.builders {
		func(i int, root context.Context) {
			builder := dicb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DowntimeIncidentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dicb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dicb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dicb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dicb *DowntimeIncidentCreateBulk) SaveX(ctx context.Context) []*DowntimeIncident {
	v, err := dicb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dicb *DowntimeIncidentCreateBulk) Exec(ctx context.Context) error {
	_, err := dicb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dicb *DowntimeIncidentCreateBulk) ExecX(ctx context.Context) {
	if err := dicb.Exec(ctx); err != nil {
		panic(err)
	}
}
